#!/usr/bin/env python3
"""
Verification script to ensure all exported files are working correctly
"""

import os
import sys
from pathlib import Path

def verify_files():
    """Verify all required files are present"""
    print("🔍 Verifying exported files...")
    
    required_files = [
        # Core pipeline files
        'defi_credit_scoring_pipeline.py',
        'defi_scoring_ui.py',
        'run_pipeline.py',
        'test_shap_pipeline.py',
        
        # Documentation
        'README.md',
        'NFCS_FORMULAS.md',
        'PIPELINE_SUMMARY.md',
        'EXPORT_SUMMARY.md',
        
        # Configuration
        'requirements.txt',
        
        # Data files
        '1. full_knowledge_graph_borrows_50K.csv',
        '2. deposits_data_processed.csv',
        '3.liquidates_data_processed.csv',
        '4. repays_data_processed.csv',
        '4. withdraws_data_processed.csv',
        
        # Generated visualizations
        'feature_distributions.png',
        'correlation_matrix.png',
        'outlier_analysis.png',
        'target_analysis.png',
        'shap_summary_plots.png'
    ]
    
    missing_files = []
    present_files = []
    
    for file in required_files:
        if os.path.exists(file):
            present_files.append(file)
            file_size = os.path.getsize(file)
            print(f"✅ {file} ({file_size:,} bytes)")
        else:
            missing_files.append(file)
            print(f"❌ {file} - MISSING")
    
    print(f"\n📊 Summary:")
    print(f"✅ Present: {len(present_files)}/{len(required_files)} files")
    if missing_files:
        print(f"❌ Missing: {len(missing_files)} files")
        print("Missing files:", missing_files)
    else:
        print("🎉 All required files are present!")
    
    return len(missing_files) == 0

def verify_imports():
    """Verify that the main pipeline can be imported"""
    print("\n🔍 Verifying Python imports...")
    
    try:
        from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
        print("✅ Main pipeline imports successfully")
        
        # Test basic initialization
        pipeline = DeFiCreditScoringPipeline(".")
        print("✅ Pipeline initialization successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def verify_dependencies():
    """Verify required dependencies are installed"""
    print("\n🔍 Verifying dependencies...")
    
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'sklearn', 'xgboost', 'scipy', 'streamlit', 
        'plotly', 'shap'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - NOT INSTALLED")
    
    if missing_packages:
        print(f"\n❌ Missing packages: {missing_packages}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("🎉 All dependencies are installed!")
        return True

def verify_data_integrity():
    """Verify data files can be loaded"""
    print("\n🔍 Verifying data integrity...")
    
    try:
        import pandas as pd
        
        data_files = [
            '1. full_knowledge_graph_borrows_50K.csv',
            '2. deposits_data_processed.csv',
            '3.liquidates_data_processed.csv',
            '4. repays_data_processed.csv',
            '4. withdraws_data_processed.csv'
        ]
        
        total_records = 0
        
        for file in data_files:
            if os.path.exists(file):
                df = pd.read_csv(file)
                records = len(df)
                total_records += records
                print(f"✅ {file}: {records:,} records")
            else:
                print(f"❌ {file}: File not found")
                return False
        
        print(f"📊 Total records across all files: {total_records:,}")
        return True
        
    except Exception as e:
        print(f"❌ Data verification error: {e}")
        return False

def verify_visualizations():
    """Verify visualization files exist and have reasonable sizes"""
    print("\n🔍 Verifying visualization files...")
    
    viz_files = [
        'feature_distributions.png',
        'correlation_matrix.png',
        'outlier_analysis.png',
        'target_analysis.png',
        'shap_summary_plots.png'
    ]
    
    for file in viz_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            if size > 1000:  # At least 1KB
                print(f"✅ {file}: {size:,} bytes")
            else:
                print(f"⚠️ {file}: {size} bytes (suspiciously small)")
        else:
            print(f"❌ {file}: Not found")
            return False
    
    return True

def main():
    """Run all verification checks"""
    print("🚀 DeFi Credit Scoring Pipeline - Export Verification")
    print("=" * 60)
    
    checks = [
        ("File Presence", verify_files),
        ("Dependencies", verify_dependencies),
        ("Python Imports", verify_imports),
        ("Data Integrity", verify_data_integrity),
        ("Visualizations", verify_visualizations)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} failed with error: {e}")
            results.append((check_name, False))
    
    # Final summary
    print("\n" + "="*60)
    print("📋 VERIFICATION SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{check_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall Result: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 ALL VERIFICATIONS PASSED!")
        print("\nYour DeFi Credit Scoring Pipeline export is complete and functional!")
        print("\nNext steps:")
        print("1. Run: python run_pipeline.py")
        print("2. Launch UI: streamlit run defi_scoring_ui.py")
        print("3. Access dashboard: http://localhost:8501")
    else:
        print("⚠️ Some verifications failed. Please check the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
