#!/usr/bin/env python3
"""
Smart Contract Data Processor
Processes smart contract interaction data for enhanced credit scoring
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import json
import ast
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class SmartContractProcessor:
    """
    Processes smart contract data to extract features for credit scoring
    """
    
    def __init__(self, smart_contracts_file: str = "smart_contracts_data_processed.csv"):
        """
        Initialize the smart contract processor
        
        Args:
            smart_contracts_file: Path to smart contracts CSV file
        """
        self.smart_contracts_file = smart_contracts_file
        self.smart_contracts_df = None
        self.processed_features = None
        
    def load_smart_contract_data(self) -> pd.DataFrame:
        """Load smart contract data from CSV"""
        logger.info("Loading smart contract data...")
        
        try:
            self.smart_contracts_df = pd.read_csv(self.smart_contracts_file)
            logger.info(f"Loaded {len(self.smart_contracts_df)} smart contract records")
            
            # Basic data info
            logger.info(f"Columns: {list(self.smart_contracts_df.columns)}")
            logger.info(f"Unique wallets: {self.smart_contracts_df['walletAddress'].nunique()}")
            logger.info(f"Unique chains: {self.smart_contracts_df['chainId'].nunique()}")
            
            return self.smart_contracts_df
            
        except Exception as e:
            logger.error(f"Error loading smart contract data: {e}")
            raise
    
    def preprocess_smart_contract_data(self) -> pd.DataFrame:
        """Preprocess smart contract data"""
        logger.info("Preprocessing smart contract data...")
        
        if self.smart_contracts_df is None:
            self.load_smart_contract_data()
        
        df = self.smart_contracts_df.copy()
        
        # Handle missing values
        df['numberOfLastDayCalls'] = pd.to_numeric(df['numberOfLastDayCalls'], errors='coerce').fillna(0)
        df['numberOfLastDayActiveUsers'] = pd.to_numeric(df['numberOfLastDayActiveUsers'], errors='coerce').fillna(0)
        df['numberOfThisMonthCalls'] = pd.to_numeric(df['numberOfThisMonthCalls'], errors='coerce').fillna(0)
        df['lastUpdatedAt'] = pd.to_numeric(df['lastUpdatedAt'], errors='coerce')
        
        # Convert timestamps
        df['lastUpdatedAt_datetime'] = pd.to_datetime(df['lastUpdatedAt'], unit='s', errors='coerce')
        df['createdAtTimestamp'] = pd.to_numeric(df['createdAtTimestamp'], errors='coerce')
        df['createdAtTimestamp_datetime'] = pd.to_datetime(df['createdAtTimestamp'], unit='s', errors='coerce')
        
        # Process chain IDs
        df['chainId'] = df['chainId'].astype(str)
        
        # Process tags (convert string representation of lists to actual lists)
        def safe_eval_list(x):
            if pd.isna(x) or x == '':
                return []
            try:
                if isinstance(x, str):
                    return ast.literal_eval(x)
                return x if isinstance(x, list) else []
            except:
                return []
        
        df['tags_list'] = df['tags'].apply(safe_eval_list)
        
        # Process daily calls data (JSON-like strings)
        def parse_daily_calls(x):
            if pd.isna(x) or x == '':
                return {}
            try:
                if isinstance(x, str):
                    # Handle malformed JSON-like strings
                    x = x.replace("'", '"')
                    return json.loads(x)
                return x if isinstance(x, dict) else {}
            except:
                return {}
        
        df['numberOfDailyCalls_dict'] = df['numberOfDailyCalls'].apply(parse_daily_calls)
        df['numberOfDailyActiveUsers_dict'] = df['numberOfDailyActiveUsers'].apply(parse_daily_calls)
        
        # Handle token-related data
        df['decimals'] = pd.to_numeric(df['decimals'], errors='coerce')
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df['marketCap'] = pd.to_numeric(df['marketCap'], errors='coerce')
        df['tradingVolume'] = pd.to_numeric(df['tradingVolume'], errors='coerce')
        df['totalSupply'] = pd.to_numeric(df['totalSupply'], errors='coerce')
        df['numberOfHolders'] = pd.to_numeric(df['numberOfHolders'], errors='coerce')
        
        self.smart_contracts_df = df
        logger.info("Smart contract data preprocessing completed")
        
        return df
    
    def extract_smart_contract_features(self) -> pd.DataFrame:
        """Extract features from smart contract data for credit scoring"""
        logger.info("Extracting smart contract features...")
        
        if self.smart_contracts_df is None:
            self.preprocess_smart_contract_data()
        
        # Group by wallet address to create wallet-level features
        wallet_features = []
        
        for wallet_address, group in self.smart_contracts_df.groupby('walletAddress'):
            features = self._extract_wallet_features(wallet_address, group)
            wallet_features.append(features)
        
        self.processed_features = pd.DataFrame(wallet_features)
        logger.info(f"Extracted features for {len(self.processed_features)} wallets")
        
        return self.processed_features
    
    def _extract_wallet_features(self, wallet_address: str, group: pd.DataFrame) -> Dict:
        """Extract features for a single wallet"""
        features = {'walletAddress': wallet_address}
        
        # Basic activity features
        features['sc_total_contracts_interacted'] = len(group)
        features['sc_unique_chains'] = group['chainId'].nunique()
        features['sc_total_last_day_calls'] = group['numberOfLastDayCalls'].sum()
        features['sc_total_month_calls'] = group['numberOfThisMonthCalls'].sum()
        features['sc_avg_last_day_calls'] = group['numberOfLastDayCalls'].mean()
        features['sc_max_last_day_calls'] = group['numberOfLastDayCalls'].max()
        
        # Contract type diversity
        all_tags = []
        for tags_list in group['tags_list']:
            all_tags.extend(tags_list)
        
        unique_tags = set(all_tags)
        features['sc_unique_contract_types'] = len(unique_tags)
        features['sc_is_contract_creator'] = int('contract' in unique_tags)
        features['sc_interacts_with_addresses'] = int('address' in unique_tags)
        
        # Chain diversity features
        chain_distribution = group['chainId'].value_counts()
        features['sc_primary_chain'] = chain_distribution.index[0] if len(chain_distribution) > 0 else '0x1'
        features['sc_chain_diversity_score'] = len(chain_distribution)
        features['sc_cross_chain_activity'] = int(len(chain_distribution) > 1)
        
        # Temporal features
        if group['lastUpdatedAt_datetime'].notna().any():
            latest_activity = group['lastUpdatedAt_datetime'].max()
            earliest_activity = group['lastUpdatedAt_datetime'].min()
            
            if pd.notna(latest_activity) and pd.notna(earliest_activity):
                features['sc_days_since_last_activity'] = (datetime.now() - latest_activity).days
                features['sc_activity_span_days'] = (latest_activity - earliest_activity).days
            else:
                features['sc_days_since_last_activity'] = 9999
                features['sc_activity_span_days'] = 0
        else:
            features['sc_days_since_last_activity'] = 9999
            features['sc_activity_span_days'] = 0
        
        # Contract creation features
        created_contracts = group[group['createdAtTimestamp'].notna()]
        features['sc_contracts_created'] = len(created_contracts)
        
        if len(created_contracts) > 0:
            features['sc_first_contract_created_days_ago'] = (
                datetime.now() - created_contracts['createdAtTimestamp_datetime'].min()
            ).days if created_contracts['createdAtTimestamp_datetime'].min() is not pd.NaT else 9999
        else:
            features['sc_first_contract_created_days_ago'] = 9999
        
        # Token-related features
        token_data = group[group['symbol'].notna()]
        features['sc_tokens_associated'] = len(token_data)
        
        if len(token_data) > 0:
            features['sc_total_market_cap'] = token_data['marketCap'].sum()
            features['sc_total_trading_volume'] = token_data['tradingVolume'].sum()
            features['sc_avg_token_price'] = token_data['price'].mean()
            features['sc_total_holders'] = token_data['numberOfHolders'].sum()
        else:
            features['sc_total_market_cap'] = 0
            features['sc_total_trading_volume'] = 0
            features['sc_avg_token_price'] = 0
            features['sc_total_holders'] = 0
        
        # Daily activity patterns
        all_daily_calls = {}
        for daily_calls_dict in group['numberOfDailyCalls_dict']:
            for date, calls in daily_calls_dict.items():
                if date in all_daily_calls:
                    all_daily_calls[date] += calls
                else:
                    all_daily_calls[date] = calls
        
        if all_daily_calls:
            daily_calls_values = list(all_daily_calls.values())
            features['sc_avg_daily_calls'] = np.mean(daily_calls_values)
            features['sc_max_daily_calls'] = np.max(daily_calls_values)
            features['sc_daily_call_variance'] = np.var(daily_calls_values)
            features['sc_active_days'] = len(daily_calls_values)
        else:
            features['sc_avg_daily_calls'] = 0
            features['sc_max_daily_calls'] = 0
            features['sc_daily_call_variance'] = 0
            features['sc_active_days'] = 0
        
        # Project diversity
        projects = group['project'].dropna().unique()
        features['sc_unique_projects'] = len(projects)
        
        # Categories diversity
        all_categories = []
        for categories in group['categories'].dropna():
            if isinstance(categories, str):
                try:
                    cats = ast.literal_eval(categories)
                    if isinstance(cats, list):
                        all_categories.extend(cats)
                except:
                    pass
        
        features['sc_unique_categories'] = len(set(all_categories))
        
        # Advanced features
        features['sc_activity_intensity'] = (
            features['sc_total_last_day_calls'] / max(features['sc_total_contracts_interacted'], 1)
        )
        
        features['sc_cross_chain_ratio'] = (
            features['sc_unique_chains'] / max(features['sc_total_contracts_interacted'], 1)
        )
        
        # Risk indicators
        features['sc_high_activity_flag'] = int(features['sc_total_last_day_calls'] > 100)
        features['sc_new_wallet_flag'] = int(features['sc_days_since_last_activity'] < 30)
        features['sc_dormant_wallet_flag'] = int(features['sc_days_since_last_activity'] > 365)
        
        return features
    
    def get_chain_specific_features(self, chain_id: str = None) -> pd.DataFrame:
        """Get features specific to a particular chain"""
        if self.smart_contracts_df is None:
            self.preprocess_smart_contract_data()
        
        if chain_id:
            chain_data = self.smart_contracts_df[self.smart_contracts_df['chainId'] == chain_id]
            logger.info(f"Extracting features for chain {chain_id}: {len(chain_data)} records")
        else:
            chain_data = self.smart_contracts_df
            logger.info("Extracting features for all chains")
        
        # Extract features for the filtered data
        wallet_features = []
        for wallet_address, group in chain_data.groupby('walletAddress'):
            features = self._extract_wallet_features(wallet_address, group)
            features['target_chain'] = chain_id
            wallet_features.append(features)
        
        return pd.DataFrame(wallet_features)
    
    def merge_with_defi_features(self, defi_features_df: pd.DataFrame) -> pd.DataFrame:
        """
        Merge smart contract features with existing DeFi features
        
        Args:
            defi_features_df: DataFrame with DeFi lending features
            
        Returns:
            Merged DataFrame with both DeFi and smart contract features
        """
        if self.processed_features is None:
            self.extract_smart_contract_features()
        
        logger.info("Merging smart contract features with DeFi features...")
        
        # Merge on wallet address
        merged_df = defi_features_df.merge(
            self.processed_features, 
            on='walletAddress', 
            how='left'
        )
        
        # Fill missing smart contract features with 0 (wallets with no smart contract activity)
        sc_columns = [col for col in self.processed_features.columns if col.startswith('sc_')]
        merged_df[sc_columns] = merged_df[sc_columns].fillna(0)
        
        logger.info(f"Merged features shape: {merged_df.shape}")
        logger.info(f"Added {len(sc_columns)} smart contract features")
        
        return merged_df
    
    def get_feature_importance_mapping(self) -> Dict[str, str]:
        """Get mapping of feature names to descriptions"""
        return {
            'sc_total_contracts_interacted': 'Total number of smart contracts interacted with',
            'sc_unique_chains': 'Number of unique blockchain networks used',
            'sc_total_last_day_calls': 'Total smart contract calls in last day',
            'sc_total_month_calls': 'Total smart contract calls this month',
            'sc_unique_contract_types': 'Diversity of contract types interacted with',
            'sc_is_contract_creator': 'Whether wallet has created contracts',
            'sc_chain_diversity_score': 'Cross-chain activity diversity score',
            'sc_cross_chain_activity': 'Whether wallet is active across multiple chains',
            'sc_days_since_last_activity': 'Days since last smart contract interaction',
            'sc_activity_span_days': 'Total span of smart contract activity',
            'sc_contracts_created': 'Number of contracts created by wallet',
            'sc_tokens_associated': 'Number of tokens associated with wallet',
            'sc_total_market_cap': 'Total market cap of associated tokens',
            'sc_avg_daily_calls': 'Average daily smart contract calls',
            'sc_active_days': 'Number of days with smart contract activity',
            'sc_unique_projects': 'Number of unique DeFi projects interacted with',
            'sc_activity_intensity': 'Intensity of smart contract activity',
            'sc_cross_chain_ratio': 'Ratio of chains to contracts',
            'sc_high_activity_flag': 'Flag for high activity wallets',
            'sc_new_wallet_flag': 'Flag for recently active wallets',
            'sc_dormant_wallet_flag': 'Flag for dormant wallets'
        }
    
    def generate_smart_contract_report(self) -> Dict:
        """Generate a comprehensive report on smart contract data"""
        if self.smart_contracts_df is None:
            self.preprocess_smart_contract_data()
        
        if self.processed_features is None:
            self.extract_smart_contract_features()
        
        report = {
            'data_summary': {
                'total_records': len(self.smart_contracts_df),
                'unique_wallets': self.smart_contracts_df['walletAddress'].nunique(),
                'unique_chains': self.smart_contracts_df['chainId'].nunique(),
                'date_range': {
                    'earliest': self.smart_contracts_df['lastUpdatedAt_datetime'].min(),
                    'latest': self.smart_contracts_df['lastUpdatedAt_datetime'].max()
                }
            },
            'chain_distribution': self.smart_contracts_df['chainId'].value_counts().to_dict(),
            'feature_statistics': self.processed_features.describe().to_dict(),
            'top_active_wallets': self.processed_features.nlargest(10, 'sc_total_last_day_calls')[
                ['walletAddress', 'sc_total_last_day_calls', 'sc_unique_chains']
            ].to_dict('records'),
            'cross_chain_wallets': len(self.processed_features[self.processed_features['sc_cross_chain_activity'] == 1])
        }
        
        return report
