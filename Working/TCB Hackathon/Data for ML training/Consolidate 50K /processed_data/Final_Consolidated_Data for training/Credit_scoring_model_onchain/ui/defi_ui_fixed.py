#!/usr/bin/env python3
"""
DeFi Credit Scoring Web UI - Fixed Version
Handles autoencoder caching issues
"""

import streamlit as st

# Page configuration - MUST be first
st.set_page_config(
    page_title="DeFi Credit Scoring Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import warnings
warnings.filterwarnings('ignore')

def get_enhanced_credit_rating(nfcs_score):
    """
    Get enhanced credit rating based on NFCS score

    Args:
        nfcs_score: NFCS score (0-1000)

    Returns:
        Dictionary with rating info
    """
    if nfcs_score >= 900:
        return {
            'rating': 'AAA',
            'description': 'Excellent',
            'detail': 'Sophisticated cross-chain DeFi users',
            'color': '#28a745',
            'emoji': '🌟'
        }
    elif nfcs_score >= 800:
        return {
            'rating': 'AA',
            'description': 'Very Good',
            'detail': 'Active multi-chain users with good repayment',
            'color': '#20c997',
            'emoji': '⭐'
        }
    elif nfcs_score >= 700:
        return {
            'rating': 'A',
            'description': 'Good',
            'detail': 'Consistent DeFi users with smart contract activity',
            'color': '#17a2b8',
            'emoji': '✅'
        }
    elif nfcs_score >= 600:
        return {
            'rating': 'BBB',
            'description': 'Fair',
            'detail': 'Regular DeFi users with moderate SC activity',
            'color': '#ffc107',
            'emoji': '⚡'
        }
    elif nfcs_score >= 500:
        return {
            'rating': 'BB',
            'description': 'Poor',
            'detail': 'Limited activity or poor repayment history',
            'color': '#fd7e14',
            'emoji': '⚠️'
        }
    elif nfcs_score >= 400:
        return {
            'rating': 'B',
            'description': 'Very Poor',
            'detail': 'Minimal activity or significant risks',
            'color': '#dc3545',
            'emoji': '🔴'
        }
    else:
        return {
            'rating': 'C',
            'description': 'Unacceptable',
            'detail': 'High risk or dormant wallets',
            'color': '#6c757d',
            'emoji': '❌'
        }

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .risk-low { color: #28a745; font-weight: bold; }
    .risk-medium { color: #ffc107; font-weight: bold; }
    .risk-high { color: #dc3545; font-weight: bold; }
    .enhanced-score {
        background: linear-gradient(90deg, #1f77b4, #17a2b8);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Global pipeline variable to avoid caching issues
if 'pipeline' not in st.session_state:
    st.session_state.pipeline = None
if 'pipeline_loaded' not in st.session_state:
    st.session_state.pipeline_loaded = False

def load_pipeline_without_cache():
    """Load pipeline without caching to avoid autoencoder issues"""
    if st.session_state.pipeline_loaded and st.session_state.pipeline is not None:
        return st.session_state.pipeline, None
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from models.defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
        
        with st.spinner("Loading DeFi Credit Scoring Pipeline..."):
            pipeline = DeFiCreditScoringPipeline("..")
            pipeline.load_data()
            pipeline.preprocess_data()
            pipeline.engineer_features()
            pipeline.create_target_variable()
            pipeline.train_models()
            
            # Store in session state
            st.session_state.pipeline = pipeline
            st.session_state.pipeline_loaded = True
            
            return pipeline, None
    except Exception as e:
        st.error(f"Error loading pipeline: {e}")
        return None, str(e)

def main():
    # Header
    st.markdown('<h1 class="main-header">🏦 DeFi Credit Scoring Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load pipeline
    pipeline, error = load_pipeline_without_cache()
    
    if pipeline is None:
        st.error(f"Failed to load pipeline: {error}")
        st.info("Please ensure all data files are present and dependencies are installed.")
        return
    
    # Success message
    if st.session_state.pipeline_loaded:
        st.success("✅ Pipeline loaded successfully with advanced algorithms!")
        
        # Show pipeline stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Wallets", f"{len(pipeline.features_df):,}")
        with col2:
            avg_nfcs = pipeline.features_df['nfcs_score'].mean()
            st.metric("Average NFCS", f"{avg_nfcs:.1f}")
        with col3:
            if hasattr(pipeline, 'anomaly_detectors'):
                st.metric("Anomaly Detectors", len(pipeline.anomaly_detectors))
        with col4:
            if hasattr(pipeline, 'clustering_models'):
                st.metric("Clustering Models", len(pipeline.clustering_models))
    
    # Navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Wallet Scoring", "Advanced Algorithms", "New User Clustering", "SHAP Explainability", "Credit Ratings"]
    )
    
    if page == "Overview":
        show_overview(pipeline)
    elif page == "Wallet Scoring":
        show_wallet_scoring(pipeline)
    elif page == "Advanced Algorithms":
        show_advanced_algorithms(pipeline)
    elif page == "New User Clustering":
        show_new_user_clustering(pipeline)
    elif page == "SHAP Explainability":
        show_shap_explainability(pipeline)
    elif page == "Credit Ratings":
        show_credit_ratings(pipeline)

def show_overview(pipeline):
    """Show overview dashboard"""
    st.header("📊 Dataset Overview")
    
    features_df = pipeline.features_df
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Wallets", f"{len(features_df):,}")
    
    with col2:
        avg_nfcs = features_df['nfcs_score'].mean()
        st.metric("Average NFCS Score", f"{avg_nfcs:.1f}")
    
    with col3:
        liquidation_rate = features_df['has_been_liquidated'].mean() * 100
        st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
    
    with col4:
        high_risk_pct = (features_df['credit_risk'] == 2).mean() * 100
        st.metric("High Risk Wallets", f"{high_risk_pct:.1f}%")
    
    # Advanced algorithms status
    st.subheader("🔬 Advanced Algorithms Status")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Anomaly Detection:**")
        if hasattr(pipeline, 'anomaly_detectors'):
            for detector in pipeline.anomaly_detectors.keys():
                st.write(f"✅ {detector.replace('_', ' ').title()}")
            
            if 'is_anomaly' in features_df.columns:
                anomaly_count = features_df['is_anomaly'].sum()
                st.metric("Total Anomalies", f"{anomaly_count:,}")
        else:
            st.write("❌ Advanced anomaly detection not available")
    
    with col2:
        st.write("**Clustering:**")
        if hasattr(pipeline, 'clustering_models'):
            for cluster_model in pipeline.clustering_models.keys():
                st.write(f"✅ {cluster_model.replace('_', ' ').title()}")
            
            if 'dbscan_cluster' in features_df.columns:
                n_clusters = features_df['dbscan_cluster'].nunique() - 1  # Exclude noise (-1)
                noise_points = (features_df['dbscan_cluster'] == -1).sum()
                st.metric("DBSCAN Clusters", f"{n_clusters}")
                st.metric("Noise Points", f"{noise_points:,}")
        else:
            st.write("❌ Advanced clustering not available")
    
    # Risk distribution
    st.subheader("Risk Distribution")
    risk_counts = features_df['credit_risk'].value_counts().sort_index()
    risk_labels = ['Low Risk', 'Medium Risk', 'High Risk']
    
    fig = px.pie(
        values=risk_counts.values,
        names=risk_labels,
        title="Credit Risk Distribution",
        color_discrete_sequence=['#28a745', '#ffc107', '#dc3545']
    )
    st.plotly_chart(fig, use_container_width=True)

def show_wallet_scoring(pipeline):
    """Show wallet scoring interface"""
    st.header("🔍 Individual Wallet Scoring")
    
    features_df = pipeline.features_df
    
    # Wallet selection
    wallet_options = features_df['walletAddress'].tolist()
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_wallet = st.selectbox(
            "Select a wallet address:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
    
    with col2:
        if st.button("Calculate NFCS Score", type="primary"):
            with st.spinner("Calculating score..."):
                try:
                    score_info = pipeline.calculate_nfcs_score(selected_wallet)
                    st.session_state.score_info = score_info
                except Exception as e:
                    st.error(f"Error calculating score: {e}")
    
    # Display results
    if hasattr(st.session_state, 'score_info') and 'error' not in st.session_state.score_info:
        score_info = st.session_state.score_info
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            nfcs_score = score_info['nfcs_score']
            st.metric("NFCS Score", f"{nfcs_score:.1f}/1000")
            
            # Score gauge
            fig = go.Figure(go.Indicator(
                mode = "gauge+number",
                value = nfcs_score,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "NFCS Score"},
                gauge = {
                    'axis': {'range': [None, 1000]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 300], 'color': "lightgray"},
                        {'range': [300, 700], 'color': "gray"},
                        {'range': [700, 1000], 'color': "lightgreen"}
                    ]
                }
            ))
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            risk_level = score_info['risk_level']
            nfcs_score = score_info['nfcs_score']

            # Get enhanced credit rating
            credit_rating = get_enhanced_credit_rating(nfcs_score)

            st.markdown(f'<p class="risk-{risk_level.lower()}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)

            # Display enhanced credit rating
            st.markdown(f"""
            <div style="background-color: {credit_rating['color']}20; padding: 1rem; border-radius: 0.5rem; border-left: 5px solid {credit_rating['color']}; margin: 1rem 0;">
                <h4 style="color: {credit_rating['color']}; margin: 0;">
                    {credit_rating['emoji']} Credit Rating: {credit_rating['rating']} - {credit_rating['description']}
                </h4>
                <p style="margin: 0.5rem 0 0 0; color: #666;">
                    {credit_rating['detail']}
                </p>
            </div>
            """, unsafe_allow_html=True)
            
            # Show advanced anomaly detection if available
            if 'advanced_anomaly_detection' in score_info:
                st.subheader("🚨 Anomaly Analysis")
                anomaly_info = score_info['advanced_anomaly_detection']
                
                anomaly_count = 0
                total_methods = 0
                
                for method, result in anomaly_info.items():
                    if isinstance(result, dict) and 'is_anomaly' in result:
                        total_methods += 1
                        if result['is_anomaly']:
                            anomaly_count += 1
                            st.write(f"🚨 {method.replace('_', ' ').title()}")
                        else:
                            st.write(f"✅ {method.replace('_', ' ').title()}")
                
                if total_methods > 0:
                    st.metric("Anomaly Score", f"{anomaly_count}/{total_methods}")
        
        with col3:
            metrics = score_info['key_metrics']
            st.subheader("Key Metrics")
            st.metric("Repayment Ratio", f"{metrics['repayment_ratio']:.3f}")
            st.metric("Repayment Consistency", f"{metrics['repayment_consistency']:.3f}")
            
            if metrics['has_been_liquidated']:
                st.error("⚠️ Wallet has been liquidated")
            else:
                st.success("✅ No liquidation history")

def show_advanced_algorithms(pipeline):
    """Show advanced algorithms analysis"""
    st.header("🔬 Advanced Algorithms Analysis")
    
    # Check if advanced algorithms are available
    has_advanced = (hasattr(pipeline, 'anomaly_detectors') and 
                   hasattr(pipeline, 'clustering_models'))
    
    if not has_advanced:
        st.warning("Advanced algorithms not yet trained. Please restart the application.")
        return
    
    st.success("✅ Advanced algorithms are available!")
    
    # Anomaly Detection Section
    st.subheader("🚨 Advanced Anomaly Detection")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Available Methods:**")
        methods = list(pipeline.anomaly_detectors.keys())
        if hasattr(pipeline, 'autoencoder') and pipeline.autoencoder:
            methods.append("autoencoder")
        
        for method in methods:
            st.write(f"✅ {method.replace('_', ' ').title()}")
    
    with col2:
        # Anomaly statistics
        if 'is_anomaly' in pipeline.features_df.columns:
            total_anomalies = pipeline.features_df['is_anomaly'].sum()
            anomaly_rate = (total_anomalies / len(pipeline.features_df)) * 100
            
            st.metric("Total Anomalies", f"{total_anomalies:,}")
            st.metric("Anomaly Rate", f"{anomaly_rate:.2f}%")
    
    # Clustering Section
    st.subheader("🔗 Advanced Clustering Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Available Methods:**")
        for method in pipeline.clustering_models.keys():
            st.write(f"✅ {method.replace('_', ' ').title()}")
    
    with col2:
        # Clustering statistics
        if 'enhanced_kmeans_cluster' in pipeline.features_df.columns:
            n_clusters = pipeline.features_df['enhanced_kmeans_cluster'].nunique()
            st.metric("K-means Clusters", n_clusters)
        
        if 'dbscan_cluster' in pipeline.features_df.columns:
            n_dbscan = pipeline.features_df['dbscan_cluster'].nunique()
            noise_points = (pipeline.features_df['dbscan_cluster'] == -1).sum()
            st.metric("DBSCAN Clusters", f"{n_dbscan-1}")
            st.metric("Noise Points", f"{noise_points:,}")

def show_new_user_clustering(pipeline):
    """Show clustering analysis for new users"""
    st.header("👥 New User Clustering Analysis")
    
    # Find users without loan history
    no_loan_users = pipeline.features_df[
        pipeline.features_df['totalAmountOfBorrowInUSD'] == 0
    ]
    
    st.subheader("📊 New User Statistics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total New Users", f"{len(no_loan_users):,}")
    
    with col2:
        total_users = len(pipeline.features_df)
        new_user_pct = (len(no_loan_users) / total_users) * 100
        st.metric("Percentage of New Users", f"{new_user_pct:.1f}%")
    
    with col3:
        if 'dbscan_initial_score' in no_loan_users.columns:
            avg_initial_score = no_loan_users['dbscan_initial_score'].mean()
            st.metric("Avg Initial Score", f"{avg_initial_score:.1f}")
    
    # Test clustering for new users
    st.subheader("🔍 Test New User Clustering")
    
    if len(no_loan_users) > 0:
        new_user_options = no_loan_users['walletAddress'].head(10).tolist()  # Limit for performance
        selected_new_user = st.selectbox(
            "Select new user for clustering analysis:",
            options=new_user_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
        
        if st.button("Analyze Clustering", type="primary"):
            try:
                cluster_result = pipeline.get_cluster_based_score(selected_new_user)
                
                if 'error' not in cluster_result:
                    st.success(f"✅ Clustering analysis for {selected_new_user[:10]}...")
                    
                    if 'cluster_assignments' in cluster_result:
                        assignments = cluster_result['cluster_assignments']
                        
                        # Display cluster assignments
                        for method, assignment in assignments.items():
                            st.subheader(f"{method.upper()} Clustering")
                            
                            if method == 'dbscan' and assignment.get('cluster_id') == -1:
                                st.warning("🔴 **Classified as Noise/Outlier**")
                                st.write(f"Suggested Initial Score: {assignment.get('suggested_initial_score', 'N/A')}")
                            else:
                                st.info(f"🔵 **Cluster ID**: {assignment.get('cluster_id', 'N/A')}")
                                
                                if 'cluster_size' in assignment:
                                    st.write(f"**Cluster Size**: {assignment['cluster_size']:,} users")
                                if 'avg_nfcs' in assignment:
                                    st.write(f"**Average NFCS**: {assignment['avg_nfcs']:.1f}")
                                if 'suggested_initial_score' in assignment:
                                    st.write(f"**Suggested Initial Score**: {assignment['suggested_initial_score']:.1f}")
                else:
                    st.error(f"Error: {cluster_result['error']}")
            except Exception as e:
                st.error(f"Error analyzing clustering: {e}")
    else:
        st.info("No new users (without loan history) found in the dataset.")

def show_shap_explainability(pipeline):
    """Show SHAP explainability analysis"""
    st.header("🔍 SHAP Explainability Analysis")

    # Check if SHAP explainer is available
    if not hasattr(pipeline, 'shap_available') or not pipeline.shap_available:
        st.warning("SHAP explainer not available. Please ensure SHAP is installed and the pipeline is properly initialized.")

        if st.button("Initialize SHAP Explainer"):
            try:
                with st.spinner("Initializing SHAP explainer..."):
                    pipeline.initialize_shap_explainer()
                if hasattr(pipeline, 'shap_available') and pipeline.shap_available:
                    st.success("✅ SHAP explainer initialized successfully!")
                    st.experimental_rerun()
                else:
                    st.error("Failed to initialize SHAP explainer")
            except Exception as e:
                st.error(f"Error initializing SHAP: {e}")
        return

    st.success("✅ SHAP explainer is available!")

    # SHAP Analysis Options
    analysis_type = st.selectbox(
        "Select SHAP Analysis Type:",
        ["Individual Explanation", "Global Feature Importance", "Model Comparison", "Interactive Analysis"]
    )

    if analysis_type == "Individual Explanation":
        st.subheader("🔍 Individual Wallet Explanation")

        # Wallet selection
        wallet_options = pipeline.features_df['walletAddress'].tolist()
        selected_wallet = st.selectbox(
            "Select wallet for SHAP explanation:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )

        if st.button("Generate SHAP Explanation", type="primary"):
            try:
                with st.spinner("Generating SHAP explanation..."):
                    explanation = pipeline._get_shap_explanation(selected_wallet)

                if explanation:
                    # Display results
                    col1, col2 = st.columns(2)

                    with col1:
                        st.subheader("SHAP Feature Importance")

                        # Get wallet score for context
                        score_info = pipeline.calculate_nfcs_score(selected_wallet)
                        if 'error' not in score_info:
                            st.write(f"**NFCS Score**: {score_info['nfcs_score']:.1f}/1000")
                            st.write(f"**Risk Level**: {score_info['risk_level']}")

                            # Enhanced credit rating
                            if 'enhanced_credit_rating' in score_info:
                                rating = score_info['enhanced_credit_rating']
                                st.write(f"**Credit Rating**: {rating['rating']} - {rating['description']}")

                        # Show prediction explanation
                        if 'prediction_explanation' in explanation:
                            st.info(explanation['prediction_explanation'])

                    with col2:
                        st.subheader("Top Contributing Features")

                        if 'top_features' in explanation:
                            top_features = explanation['top_features'][:10]

                            # Create DataFrame for plotting
                            features_df = pd.DataFrame(top_features)

                            # Create SHAP values chart
                            fig = px.bar(
                                features_df,
                                x='shap_value',
                                y='feature',
                                orientation='h',
                                title="SHAP Feature Contributions",
                                color='shap_value',
                                color_continuous_scale='RdBu_r'
                            )
                            fig.update_layout(height=400, yaxis={'categoryorder': 'total ascending'})
                            st.plotly_chart(fig, use_container_width=True)

                    # Detailed explanation
                    st.subheader("📊 Detailed SHAP Analysis")

                    col1, col2 = st.columns(2)

                    with col1:
                        st.write("**🔴 Risk Increasing Factors:**")
                        if 'feature_contributions' in explanation and 'positive' in explanation['feature_contributions']:
                            positive_factors = explanation['feature_contributions']['positive']
                            for factor in positive_factors:
                                direction = "↑"
                                st.write(f"{direction} **{factor['feature']}**: {factor['shap_value']:.4f}")
                                st.write(f"   Value: {factor['feature_value']:.4f}")
                                st.write("---")

                    with col2:
                        st.write("**🟢 Risk Reducing Factors:**")
                        if 'feature_contributions' in explanation and 'negative' in explanation['feature_contributions']:
                            negative_factors = explanation['feature_contributions']['negative']
                            for factor in negative_factors:
                                direction = "↓"
                                st.write(f"{direction} **{factor['feature']}**: {factor['shap_value']:.4f}")
                                st.write(f"   Value: {factor['feature_value']:.4f}")
                                st.write("---")

                else:
                    st.error("No SHAP explanation available for this wallet.")

            except Exception as e:
                st.error(f"Error generating SHAP explanation: {e}")

    elif analysis_type == "Global Feature Importance":
        st.subheader("🌍 Global Feature Importance")

        model_choice = st.selectbox("Select Model:", ["random_forest", "xgboost"])

        if st.button("Generate Feature Importance", type="primary"):
            try:
                with st.spinner("Calculating feature importance..."):
                    # Use model's built-in feature importance
                    if model_choice == "random_forest" and 'random_forest' in pipeline.models:
                        model = pipeline.models['random_forest']
                        feature_importance = model.feature_importances_
                    elif model_choice == "xgboost" and 'xgboost' in pipeline.models:
                        model = pipeline.models['xgboost']
                        feature_importance = model.feature_importances_
                    else:
                        st.error("Selected model not available")
                        return

                    # Create importance DataFrame
                    importance_df = pd.DataFrame({
                        'feature': pipeline.feature_cols,
                        'importance': feature_importance
                    }).sort_values('importance', ascending=False)

                # Display top features
                top_n = st.slider("Number of top features to show:", 10, 50, 20)
                top_features = importance_df.head(top_n)

                # Create interactive plot
                fig = px.bar(
                    top_features,
                    x='importance',
                    y='feature',
                    orientation='h',
                    title=f'Top {top_n} Feature Importance - {model_choice.title()}',
                    labels={'importance': 'Mean |SHAP Value|', 'feature': 'Features'}
                )
                fig.update_layout(height=600, yaxis={'categoryorder': 'total ascending'})
                st.plotly_chart(fig, use_container_width=True)

                # Show data table
                st.subheader("📋 Feature Importance Data")
                st.dataframe(top_features, use_container_width=True)

            except Exception as e:
                st.error(f"Error calculating feature importance: {e}")

    elif analysis_type == "Model Comparison":
        st.subheader("⚖️ Model Comparison")

        if st.button("Compare Models", type="primary"):
            try:
                with st.spinner("Comparing models..."):
                    # Get feature importance for all models
                    comparison_data = []

                    for model_name in ['random_forest', 'xgboost']:
                        if model_name in pipeline.shap_explainer.explainers:
                            importance_df = pipeline.shap_explainer.get_feature_importance(model_name)
                            importance_df['model'] = model_name
                            comparison_data.append(importance_df.head(15))

                    if comparison_data:
                        combined_df = pd.concat(comparison_data, ignore_index=True)

                        # Create comparison plot
                        fig = px.bar(
                            combined_df,
                            x='importance',
                            y='feature',
                            color='model',
                            orientation='h',
                            title='Feature Importance Comparison Across Models',
                            labels={'importance': 'Mean |SHAP Value|', 'feature': 'Features'}
                        )
                        fig.update_layout(height=800)
                        st.plotly_chart(fig, use_container_width=True)

                        # Show correlation between models
                        if len(comparison_data) > 1:
                            pivot_df = combined_df.pivot(index='feature', columns='model', values='importance').fillna(0)
                            correlation = pivot_df.corr()

                            st.subheader("📊 Model Agreement")
                            fig_corr = px.imshow(
                                correlation,
                                title="Feature Importance Correlation Between Models",
                                color_continuous_scale='RdBu_r'
                            )
                            st.plotly_chart(fig_corr, use_container_width=True)

            except Exception as e:
                st.error(f"Error in model comparison: {e}")

    elif analysis_type == "Interactive Analysis":
        st.subheader("🎮 Interactive SHAP Analysis")

        model_choice = st.selectbox("Select Model for Interactive Analysis:", ["random_forest", "xgboost"])

        if st.button("Generate Interactive Analysis", type="primary"):
            try:
                with st.spinner("Creating interactive analysis..."):
                    interactive_fig = pipeline.shap_explainer.create_interactive_shap_plot(model_choice)
                    st.plotly_chart(interactive_fig, use_container_width=True)

            except Exception as e:
                st.error(f"Error creating interactive analysis: {e}")

def show_credit_ratings(pipeline):
    """Show credit ratings analysis and distribution"""
    st.header("🏆 Enhanced Credit Ratings Analysis")

    # Calculate credit ratings for all wallets
    features_df = pipeline.features_df.copy()

    # Apply enhanced credit rating to all wallets
    features_df['credit_rating_info'] = features_df['nfcs_score'].apply(get_enhanced_credit_rating)
    features_df['credit_rating'] = features_df['credit_rating_info'].apply(lambda x: x['rating'])
    features_df['rating_description'] = features_df['credit_rating_info'].apply(lambda x: x['description'])

    # Credit Rating Distribution
    st.subheader("📊 Credit Rating Distribution")

    rating_counts = features_df['credit_rating'].value_counts()
    rating_order = ['AAA', 'AA', 'A', 'BBB', 'BB', 'B', 'C']
    rating_counts = rating_counts.reindex(rating_order, fill_value=0)

    col1, col2 = st.columns(2)

    with col1:
        # Pie chart
        fig_pie = px.pie(
            values=rating_counts.values,
            names=rating_counts.index,
            title="Credit Rating Distribution",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        st.plotly_chart(fig_pie, use_container_width=True)

    with col2:
        # Bar chart
        fig_bar = px.bar(
            x=rating_counts.index,
            y=rating_counts.values,
            title="Credit Rating Counts",
            labels={'x': 'Credit Rating', 'y': 'Number of Wallets'},
            color=rating_counts.values,
            color_continuous_scale='Viridis'
        )
        st.plotly_chart(fig_bar, use_container_width=True)

    # Rating Statistics
    st.subheader("📈 Rating Statistics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        excellent_pct = (rating_counts['AAA'] / len(features_df)) * 100
        st.metric("Excellent (AAA)", f"{rating_counts['AAA']:,}", f"{excellent_pct:.1f}%")

    with col2:
        good_pct = ((rating_counts['AA'] + rating_counts['A']) / len(features_df)) * 100
        st.metric("Good (AA-A)", f"{rating_counts['AA'] + rating_counts['A']:,}", f"{good_pct:.1f}%")

    with col3:
        fair_pct = (rating_counts['BBB'] / len(features_df)) * 100
        st.metric("Fair (BBB)", f"{rating_counts['BBB']:,}", f"{fair_pct:.1f}%")

    with col4:
        poor_pct = ((rating_counts['BB'] + rating_counts['B'] + rating_counts['C']) / len(features_df)) * 100
        st.metric("Poor (BB-C)", f"{rating_counts['BB'] + rating_counts['B'] + rating_counts['C']:,}", f"{poor_pct:.1f}%")

    # Rating Details
    st.subheader("🎯 Credit Rating Details")

    rating_details = []
    for rating in rating_order:
        sample_rating = get_enhanced_credit_rating(900 if rating == 'AAA' else
                                                 850 if rating == 'AA' else
                                                 750 if rating == 'A' else
                                                 650 if rating == 'BBB' else
                                                 550 if rating == 'BB' else
                                                 450 if rating == 'B' else 300)

        rating_details.append({
            'Rating': f"{sample_rating['emoji']} {rating}",
            'Description': sample_rating['description'],
            'Details': sample_rating['detail'],
            'Count': rating_counts[rating],
            'Percentage': f"{(rating_counts[rating] / len(features_df)) * 100:.1f}%"
        })

    rating_df = pd.DataFrame(rating_details)
    st.dataframe(rating_df, use_container_width=True)

    # NFCS Score vs Credit Rating
    st.subheader("📊 NFCS Score vs Credit Rating")

    fig_scatter = px.box(
        features_df,
        x='credit_rating',
        y='nfcs_score',
        title="NFCS Score Distribution by Credit Rating",
        category_orders={'credit_rating': rating_order}
    )
    st.plotly_chart(fig_scatter, use_container_width=True)

    # Sample wallets by rating
    st.subheader("🔍 Sample Wallets by Rating")

    selected_rating = st.selectbox("Select Credit Rating:", rating_order)

    rating_wallets = features_df[features_df['credit_rating'] == selected_rating]

    if len(rating_wallets) > 0:
        sample_wallets = rating_wallets.head(10)[['walletAddress', 'nfcs_score', 'credit_risk', 'has_been_liquidated']]
        sample_wallets['walletAddress'] = sample_wallets['walletAddress'].apply(lambda x: f"{x[:10]}...{x[-8:]}")

        st.dataframe(sample_wallets, use_container_width=True)

        # Rating insights
        avg_score = rating_wallets['nfcs_score'].mean()
        liquidation_rate = rating_wallets['has_been_liquidated'].mean() * 100

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Average NFCS Score", f"{avg_score:.1f}")
        with col2:
            st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
        with col3:
            st.metric("Total Wallets", f"{len(rating_wallets):,}")
    else:
        st.info(f"No wallets found with {selected_rating} rating.")

if __name__ == "__main__":
    main()
