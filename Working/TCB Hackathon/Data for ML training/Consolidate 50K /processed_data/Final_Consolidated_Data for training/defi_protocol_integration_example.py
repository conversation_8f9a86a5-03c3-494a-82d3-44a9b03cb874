#!/usr/bin/env python3
"""
DeFi Protocol Integration Example
Demonstrates how to integrate the ML pipeline with smart contracts for automated scoring
"""

import asyncio
import json
import time
from typing import Dict, List
import logging

# Import our integration modules
from enhanced_defi_pipeline import EnhancedDeFiPipeline
from blockchain_integration import BlockchainIntegration
from smart_contract_processor import SmartContractProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeFiProtocolIntegration:
    """
    Complete DeFi Protocol Integration Example
    Shows how to implement automated scoring, oracle updates, and cross-chain compatibility
    """
    
    def __init__(self):
        """Initialize the integration system"""
        
        # Blockchain configuration (replace with actual RPC URLs)
        self.blockchain_config = {
            "ethereum_rpc": "https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY",
            "bsc_rpc": "https://bsc-dataseed.binance.org/",
            "polygon_rpc": "https://polygon-rpc.com/",
            "arbitrum_rpc": "https://arb1.arbitrum.io/rpc",
            "optimism_rpc": "https://mainnet.optimism.io"
        }
        
        # Contract addresses (replace with actual deployed addresses)
        self.contract_addresses = {
            1: {  # Ethereum
                "nfcs_oracle": "******************************************",
                "lending_contract": "******************************************"
            },
            56: {  # BSC
                "nfcs_oracle": "******************************************",
                "lending_contract": "******************************************"
            },
            137: {  # Polygon
                "nfcs_oracle": "******************************************",
                "lending_contract": "******************************************"
            }
        }
        
        # Initialize components
        self.enhanced_pipeline = None
        self.blockchain_integration = None
        self.oracle_updater_key = None  # Set this to your private key for oracle updates
        
    async def initialize_system(self):
        """Initialize the complete integration system"""
        logger.info("🚀 Initializing DeFi Protocol Integration System...")
        
        # 1. Initialize Enhanced ML Pipeline
        logger.info("Step 1: Initializing Enhanced ML Pipeline...")
        self.enhanced_pipeline = EnhancedDeFiPipeline(".", self.blockchain_config)
        self.enhanced_pipeline.run_enhanced_pipeline()
        
        # 2. Initialize Blockchain Integration
        logger.info("Step 2: Initializing Blockchain Integration...")
        self.blockchain_integration = BlockchainIntegration(self.blockchain_config)
        self.blockchain_integration.load_ml_pipeline(".")
        
        # 3. Load Smart Contracts
        logger.info("Step 3: Loading Smart Contracts...")
        await self._load_smart_contracts()
        
        logger.info("✅ System initialization completed!")
    
    async def _load_smart_contracts(self):
        """Load smart contracts for all supported chains"""
        for chain_id, contracts in self.contract_addresses.items():
            try:
                # Load NFCS Oracle
                self.blockchain_integration.load_contract(
                    chain_id=chain_id,
                    contract_address=contracts["nfcs_oracle"],
                    abi_path="contracts/NFCSOracle_abi.json"
                )
                
                logger.info(f"✅ Loaded contracts for chain {chain_id}")
                
            except Exception as e:
                logger.error(f"❌ Failed to load contracts for chain {chain_id}: {e}")
    
    async def automated_scoring_workflow(self, wallet_addresses: List[str]):
        """
        Automated scoring workflow for multiple wallets
        
        Args:
            wallet_addresses: List of wallet addresses to score
        """
        logger.info(f"🔄 Starting automated scoring for {len(wallet_addresses)} wallets...")
        
        results = []
        
        for wallet_address in wallet_addresses:
            try:
                # 1. Calculate Enhanced NFCS Score
                logger.info(f"Calculating NFCS for {wallet_address[:10]}...")
                enhanced_score = self.enhanced_pipeline.calculate_enhanced_nfcs_score(wallet_address)
                
                if 'error' in enhanced_score:
                    logger.warning(f"⚠️ Could not score {wallet_address}: {enhanced_score['error']}")
                    continue
                
                # 2. Update Oracle on Multiple Chains
                oracle_updates = await self._update_oracle_cross_chain(wallet_address, enhanced_score)
                
                # 3. Check Lending Eligibility
                eligibility_results = await self._check_lending_eligibility_cross_chain(wallet_address)
                
                result = {
                    'wallet_address': wallet_address,
                    'enhanced_nfcs_score': enhanced_score['enhanced_nfcs_score'],
                    'risk_level': enhanced_score['risk_level'],
                    'oracle_updates': oracle_updates,
                    'lending_eligibility': eligibility_results,
                    'smart_contract_insights': enhanced_score['smart_contract_insights']
                }
                
                results.append(result)
                
                logger.info(f"✅ Completed scoring for {wallet_address[:10]}... (NFCS: {enhanced_score['enhanced_nfcs_score']:.2f})")
                
                # Rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ Error processing {wallet_address}: {e}")
                results.append({
                    'wallet_address': wallet_address,
                    'error': str(e)
                })
        
        return results
    
    async def _update_oracle_cross_chain(self, wallet_address: str, enhanced_score: Dict) -> Dict:
        """Update NFCS score across multiple chains"""
        oracle_updates = {}
        
        if not self.oracle_updater_key:
            logger.warning("⚠️ No oracle updater key provided - skipping oracle updates")
            return {"error": "No oracle updater key"}
        
        for chain_id in [1, 56, 137]:  # Ethereum, BSC, Polygon
            try:
                if chain_id in self.contract_addresses:
                    oracle_address = self.contract_addresses[chain_id]["nfcs_oracle"]
                    
                    update_result = self.blockchain_integration.update_oracle_score(
                        chain_id=chain_id,
                        oracle_contract_address=oracle_address,
                        wallet_address=wallet_address,
                        private_key=self.oracle_updater_key
                    )
                    
                    oracle_updates[f"chain_{chain_id}"] = update_result
                    
                    if update_result.get("success"):
                        logger.info(f"✅ Updated oracle on chain {chain_id}: {update_result['transaction_hash']}")
                    else:
                        logger.error(f"❌ Failed to update oracle on chain {chain_id}: {update_result.get('error')}")
                
            except Exception as e:
                logger.error(f"❌ Oracle update error for chain {chain_id}: {e}")
                oracle_updates[f"chain_{chain_id}"] = {"error": str(e)}
        
        return oracle_updates
    
    async def _check_lending_eligibility_cross_chain(self, wallet_address: str) -> Dict:
        """Check lending eligibility across multiple chains"""
        eligibility_results = {}
        
        for chain_id in [1, 56, 137]:  # Ethereum, BSC, Polygon
            try:
                if chain_id in self.contract_addresses:
                    lending_address = self.contract_addresses[chain_id]["lending_contract"]
                    
                    eligibility = self.blockchain_integration.check_lending_eligibility(
                        chain_id=chain_id,
                        lending_contract_address=lending_address,
                        wallet_address=wallet_address,
                        min_score=400  # Minimum score for lending
                    )
                    
                    eligibility_results[f"chain_{chain_id}"] = eligibility
                
            except Exception as e:
                logger.error(f"❌ Eligibility check error for chain {chain_id}: {e}")
                eligibility_results[f"chain_{chain_id}"] = {"error": str(e)}
        
        return eligibility_results
    
    async def real_time_monitoring(self):
        """Real-time monitoring of oracle updates and lending activities"""
        logger.info("🔍 Starting real-time monitoring...")
        
        def oracle_update_callback(event):
            """Callback for oracle update events"""
            logger.info(f"📊 Oracle Update Detected:")
            logger.info(f"   Wallet: {event['args']['wallet']}")
            logger.info(f"   Score: {event['args']['score']}")
            logger.info(f"   Risk Level: {event['args']['riskLevel']}")
            logger.info(f"   Block: {event['blockNumber']}")
        
        # Monitor oracle updates on all chains
        monitoring_tasks = []
        
        for chain_id in [1, 56, 137]:
            if chain_id in self.contract_addresses:
                oracle_address = self.contract_addresses[chain_id]["nfcs_oracle"]
                
                task = asyncio.create_task(
                    self._monitor_chain_async(chain_id, oracle_address, oracle_update_callback)
                )
                monitoring_tasks.append(task)
        
        # Run monitoring tasks
        try:
            await asyncio.gather(*monitoring_tasks)
        except KeyboardInterrupt:
            logger.info("🛑 Monitoring stopped by user")
    
    async def _monitor_chain_async(self, chain_id: int, oracle_address: str, callback):
        """Async wrapper for chain monitoring"""
        await asyncio.get_event_loop().run_in_executor(
            None,
            self.blockchain_integration.monitor_oracle_updates,
            chain_id,
            oracle_address,
            callback
        )
    
    async def cross_chain_score_sync(self, wallet_address: str, source_chain: int = 1):
        """Synchronize NFCS scores across chains"""
        logger.info(f"🔄 Syncing scores for {wallet_address[:10]}... from chain {source_chain}")
        
        if not self.oracle_updater_key:
            logger.warning("⚠️ No oracle updater key provided - cannot sync scores")
            return {"error": "No oracle updater key"}
        
        target_chains = [56, 137]  # Sync to BSC and Polygon from Ethereum
        
        sync_result = self.blockchain_integration.sync_scores_across_chains(
            wallet_address=wallet_address,
            source_chain_id=source_chain,
            target_chain_ids=target_chains,
            private_key=self.oracle_updater_key
        )
        
        logger.info(f"✅ Cross-chain sync completed for {wallet_address[:10]}...")
        return sync_result
    
    async def batch_oracle_update(self, wallet_addresses: List[str], chain_id: int = 1):
        """Batch update oracle scores for multiple wallets"""
        logger.info(f"📦 Batch updating {len(wallet_addresses)} wallets on chain {chain_id}...")
        
        if not self.oracle_updater_key:
            logger.warning("⚠️ No oracle updater key provided - cannot update oracle")
            return {"error": "No oracle updater key"}
        
        oracle_address = self.contract_addresses[chain_id]["nfcs_oracle"]
        
        batch_results = self.blockchain_integration.batch_update_scores(
            chain_id=chain_id,
            oracle_contract_address=oracle_address,
            wallet_addresses=wallet_addresses,
            private_key=self.oracle_updater_key
        )
        
        successful_updates = sum(1 for result in batch_results if result.get("success"))
        logger.info(f"✅ Batch update completed: {successful_updates}/{len(wallet_addresses)} successful")
        
        return batch_results
    
    def generate_integration_report(self, scoring_results: List[Dict]) -> Dict:
        """Generate comprehensive integration report"""
        logger.info("📊 Generating integration report...")
        
        total_wallets = len(scoring_results)
        successful_scores = sum(1 for r in scoring_results if 'enhanced_nfcs_score' in r)
        
        # Risk distribution
        risk_distribution = {}
        for result in scoring_results:
            if 'risk_level' in result:
                risk = result['risk_level']
                risk_distribution[risk] = risk_distribution.get(risk, 0) + 1
        
        # Cross-chain activity analysis
        cross_chain_users = 0
        for result in scoring_results:
            if 'smart_contract_insights' in result:
                if result['smart_contract_insights']['smart_contract_activity']['cross_chain_user']:
                    cross_chain_users += 1
        
        # Oracle update success rates
        oracle_success_rates = {}
        for result in scoring_results:
            if 'oracle_updates' in result:
                for chain, update_result in result['oracle_updates'].items():
                    if chain not in oracle_success_rates:
                        oracle_success_rates[chain] = {'success': 0, 'total': 0}
                    
                    oracle_success_rates[chain]['total'] += 1
                    if update_result.get('success'):
                        oracle_success_rates[chain]['success'] += 1
        
        report = {
            'summary': {
                'total_wallets_processed': total_wallets,
                'successful_scores': successful_scores,
                'success_rate': successful_scores / total_wallets if total_wallets > 0 else 0,
                'cross_chain_users': cross_chain_users,
                'cross_chain_percentage': cross_chain_users / successful_scores if successful_scores > 0 else 0
            },
            'risk_distribution': risk_distribution,
            'oracle_success_rates': oracle_success_rates,
            'timestamp': time.time()
        }
        
        logger.info("✅ Integration report generated")
        return report

# Example usage and demonstration
async def main():
    """Main demonstration function"""
    print("🏦 DeFi Protocol Integration Demo")
    print("=" * 50)
    
    # Initialize integration system
    integration = DeFiProtocolIntegration()
    
    # Note: In a real implementation, you would:
    # 1. Set integration.oracle_updater_key to your private key
    # 2. Update blockchain_config with real RPC URLs
    # 3. Update contract_addresses with deployed contract addresses
    
    try:
        # Initialize system
        await integration.initialize_system()
        
        # Get sample wallets from the enhanced pipeline
        sample_wallets = integration.enhanced_pipeline.enhanced_features_df['walletAddress'].head(5).tolist()
        
        print(f"\n📋 Processing {len(sample_wallets)} sample wallets...")
        
        # Run automated scoring workflow
        scoring_results = await integration.automated_scoring_workflow(sample_wallets)
        
        # Generate and display report
        report = integration.generate_integration_report(scoring_results)
        
        print("\n📊 Integration Report:")
        print(f"Total Wallets: {report['summary']['total_wallets_processed']}")
        print(f"Success Rate: {report['summary']['success_rate']:.1%}")
        print(f"Cross-chain Users: {report['summary']['cross_chain_users']}")
        print(f"Risk Distribution: {report['risk_distribution']}")
        
        # Display sample results
        print("\n🎯 Sample Scoring Results:")
        for i, result in enumerate(scoring_results[:3], 1):
            if 'enhanced_nfcs_score' in result:
                print(f"{i}. {result['wallet_address'][:10]}...")
                print(f"   Enhanced NFCS: {result['enhanced_nfcs_score']:.2f}")
                print(f"   Risk Level: {result['risk_level']}")
                print(f"   Cross-chain: {result['smart_contract_insights']['smart_contract_activity']['cross_chain_user']}")
        
        print("\n✅ Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
