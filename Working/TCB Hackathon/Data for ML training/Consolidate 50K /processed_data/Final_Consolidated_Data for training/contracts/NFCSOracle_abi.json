[{"inputs": [{"internalType": "bytes32", "name": "_initialModelVersion", "type": "bytes32"}, {"internalType": "uint256", "name": "_minConfidence", "type": "uint256"}, {"internalType": "uint256", "name": "_maxScoreAge", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "chainId", "type": "uint256"}], "name": "CrossChainScoreRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "oldVersion", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newVersion", "type": "bytes32"}], "name": "ModelVersionUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newOracle", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "OracleUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "wallet", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "score", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "riskLevel", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "confidence", "type": "uint256"}], "name": "ScoreUpdated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "Unpaused", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_updater", "type": "address"}], "name": "addAuthorizedUpdater", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_chainId", "type": "uint256"}, {"internalType": "address", "name": "_oracle", "type": "address"}], "name": "addSupportedChain", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "authorizedUpdaters", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_wallets", "type": "address[]"}, {"internalType": "uint256[]", "name": "_scores", "type": "uint256[]"}, {"internalType": "uint8[]", "name": "_riskLevels", "type": "uint8[]"}, {"internalType": "uint256[]", "name": "_confidences", "type": "uint256[]"}], "name": "batchUpdateScores", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "chainIds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "chainOracles", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "uint256", "name": "minConfidence", "type": "uint256"}, {"internalType": "uint256", "name": "maxScoreAge", "type": "uint256"}, {"internalType": "bool", "name": "requireSignature", "type": "bool"}, {"internalType": "address", "name": "fallback<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentModelVersion", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}], "name": "getScore", "outputs": [{"internalType": "uint256", "name": "score", "type": "uint256"}, {"internalType": "uint8", "name": "riskLevel", "type": "uint8"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bool", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bool"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSupportedChains", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}, {"internalType": "uint256", "name": "_minScore", "type": "uint256"}], "name": "isEligibleForLending", "outputs": [{"internalType": "bool", "name": "eligible", "type": "bool"}, {"internalType": "uint256", "name": "currentScore", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_updater", "type": "address"}], "name": "removeAuthorizedUpdater", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}, {"internalType": "uint256", "name": "_<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "requestCrossChainScore", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "scoreValidityPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalScoresUpdated", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_minConfidence", "type": "uint256"}, {"internalType": "uint256", "name": "_maxScoreAge", "type": "uint256"}, {"internalType": "bool", "name": "_requireSignature", "type": "bool"}, {"internalType": "address", "name": "_fallback<PERSON><PERSON>le", "type": "address"}], "name": "updateConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_newVersion", "type": "bytes32"}], "name": "updateModelVersion", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_wallet", "type": "address"}, {"internalType": "uint256", "name": "_score", "type": "uint256"}, {"internalType": "uint8", "name": "_riskLevel", "type": "uint8"}, {"internalType": "uint256", "name": "_confidence", "type": "uint256"}], "name": "updateScore", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "walletScores", "outputs": [{"internalType": "uint256", "name": "score", "type": "uint256"}, {"internalType": "uint8", "name": "riskLevel", "type": "uint8"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "bytes32", "name": "modelVersion", "type": "bytes32"}], "stateMutability": "view", "type": "function"}]