#!/usr/bin/env python3
"""
DeFi Credit Scoring ML Pipeline
Based on the guide for building ML models for undercollateralized DeFi lending
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# ML Libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.cluster import KMeans, DBSCAN
import xgboost as xgb
from scipy import stats
from scipy.stats import boxcox

# Suppress warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeFiCreditScoringPipeline:
    """
    Complete ML Pipeline for DeFi Credit Scoring
    Implements the methodology from <PERSON>'s article on undercollateralized DeFi lending
    """
    
    def __init__(self, data_path: str = "."):
        """
        Initialize the pipeline
        
        Args:
            data_path: Path to the directory containing CSV files
        """
        self.data_path = data_path
        self.borrows_df = None
        self.deposits_df = None
        self.repays_df = None
        self.liquidates_df = None
        self.withdraws_df = None
        self.consolidated_df = None
        self.features_df = None
        self.scaler = StandardScaler()
        self.models = {}
        self.feature_importance = {}
        
        # Set plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def load_data(self) -> None:
        """Load all CSV files"""
        logger.info("Loading data files...")
        
        try:
            self.borrows_df = pd.read_csv(f"{self.data_path}/1. full_knowledge_graph_borrows_50K.csv")
            self.deposits_df = pd.read_csv(f"{self.data_path}/2. deposits_data_processed.csv")
            self.liquidates_df = pd.read_csv(f"{self.data_path}/3.liquidates_data_processed.csv")
            self.repays_df = pd.read_csv(f"{self.data_path}/4. repays_data_processed.csv")
            self.withdraws_df = pd.read_csv(f"{self.data_path}/4. withdraws_data_processed.csv")
            
            logger.info(f"Loaded data shapes:")
            logger.info(f"Borrows: {self.borrows_df.shape}")
            logger.info(f"Deposits: {self.deposits_df.shape}")
            logger.info(f"Repays: {self.repays_df.shape}")
            logger.info(f"Liquidates: {self.liquidates_df.shape}")
            logger.info(f"Withdraws: {self.withdraws_df.shape}")
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def preprocess_data(self) -> None:
        """
        Comprehensive data preprocessing following the guide:
        - Handle missing values
        - Remove duplicates
        - Fix data inconsistencies
        - Handle outliers
        - Handle skewed data
        - Process timestamps
        """
        logger.info("Starting data preprocessing...")
        
        # Process each dataset
        self._preprocess_borrows()
        self._preprocess_deposits()
        self._preprocess_repays()
        self._preprocess_liquidates()
        self._preprocess_withdraws()
        
        # Consolidate all data
        self._consolidate_data()
        
        logger.info("Data preprocessing completed")
    
    def _preprocess_borrows(self) -> None:
        """Preprocess borrows data"""
        logger.info("Preprocessing borrows data...")
        
        # Handle timestamp
        if 'timestamp' in self.borrows_df.columns:
            self.borrows_df['timestamp'] = pd.to_datetime(self.borrows_df['timestamp'], unit='s', errors='coerce')
            self.borrows_df['borrow_year'] = self.borrows_df['timestamp'].dt.year
            self.borrows_df['borrow_month'] = self.borrows_df['timestamp'].dt.month
            self.borrows_df['borrow_day_of_week'] = self.borrows_df['timestamp'].dt.dayofweek
        
        # Handle missing values
        numeric_cols = ['valueInUSD', 'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD', 
                       'highestBorrowInUSD', 'lowestBorrowInUSD', 'averageBorrowInUSD']
        
        for col in numeric_cols:
            if col in self.borrows_df.columns:
                self.borrows_df[col] = pd.to_numeric(self.borrows_df[col], errors='coerce')
                self.borrows_df[col].fillna(self.borrows_df[col].median(), inplace=True)
        
        # Remove duplicates based on wallet address and timestamp
        initial_shape = self.borrows_df.shape[0]
        self.borrows_df.drop_duplicates(subset=['walletAddress', 'timestamp'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.borrows_df.shape[0]} duplicate borrow records")
        
        # Handle outliers using IQR method
        self._handle_outliers(self.borrows_df, numeric_cols)
        
        # Handle skewed data
        self._handle_skewness(self.borrows_df, numeric_cols)
    
    def _preprocess_deposits(self) -> None:
        """Preprocess deposits data"""
        logger.info("Preprocessing deposits data...")
        
        # Handle missing values in deposits
        numeric_cols = ['totalNumberOfDeposit', 'totalAmountOfDepositInUSD', 
                       'highestDepositInUSD', 'lowestDepositInUSD', 'averageDepositInUSD']
        
        for col in numeric_cols:
            if col in self.deposits_df.columns:
                self.deposits_df[col] = pd.to_numeric(self.deposits_df[col], errors='coerce')
                self.deposits_df[col].fillna(self.deposits_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.deposits_df.shape[0]
        self.deposits_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.deposits_df.shape[0]} duplicate deposit records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.deposits_df, numeric_cols)
        self._handle_skewness(self.deposits_df, numeric_cols)
    
    def _preprocess_repays(self) -> None:
        """Preprocess repays data"""
        logger.info("Preprocessing repays data...")
        
        numeric_cols = ['totalAmountOfRepayInUSD', 'totalNumberOfRepay', 
                       'highestRepayInUSD', 'lowestRepayInUSD', 'averageRepayInUSD']
        
        for col in numeric_cols:
            if col in self.repays_df.columns:
                self.repays_df[col] = pd.to_numeric(self.repays_df[col], errors='coerce')
                self.repays_df[col].fillna(self.repays_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.repays_df.shape[0]
        self.repays_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.repays_df.shape[0]} duplicate repay records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.repays_df, numeric_cols)
        self._handle_skewness(self.repays_df, numeric_cols)
    
    def _preprocess_liquidates(self) -> None:
        """Preprocess liquidates data"""
        logger.info("Preprocessing liquidates data...")
        
        # Handle missing values and duplicates for liquidates
        if not self.liquidates_df.empty:
            initial_shape = self.liquidates_df.shape[0]
            self.liquidates_df.drop_duplicates(subset=['walletAddress'], keep='first', inplace=True)
            logger.info(f"Removed {initial_shape - self.liquidates_df.shape[0]} duplicate liquidation records")
    
    def _preprocess_withdraws(self) -> None:
        """Preprocess withdraws data"""
        logger.info("Preprocessing withdraws data...")
        
        numeric_cols = ['totalNumberOfWithdraw', 'totalAmountOfWithdrawInUSD', 
                       'highestWithdrawInUSD', 'lowestWithdrawInUSD', 'averageWithdrawInUSD']
        
        for col in numeric_cols:
            if col in self.withdraws_df.columns:
                self.withdraws_df[col] = pd.to_numeric(self.withdraws_df[col], errors='coerce')
                self.withdraws_df[col].fillna(self.withdraws_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.withdraws_df.shape[0]
        self.withdraws_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.withdraws_df.shape[0]} duplicate withdraw records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.withdraws_df, numeric_cols)
        self._handle_skewness(self.withdraws_df, numeric_cols)
    
    def _handle_outliers(self, df: pd.DataFrame, numeric_cols: List[str]) -> None:
        """Handle outliers using IQR method"""
        for col in numeric_cols:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Cap outliers instead of removing them to preserve data
                df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
    
    def _handle_skewness(self, df: pd.DataFrame, numeric_cols: List[str]) -> None:
        """Handle skewed data using log transformation for right skew and box-cox for left skew"""
        for col in numeric_cols:
            if col in df.columns and df[col].dtype in ['float64', 'int64']:
                skewness = stats.skew(df[col].dropna())
                
                if abs(skewness) > 1:  # Significant skewness
                    if skewness > 1:  # Right skewed
                        # Log transformation (add 1 to handle zeros)
                        df[f'{col}_log'] = np.log1p(df[col])
                    elif skewness < -1:  # Left skewed
                        # Box-Cox transformation (requires positive values)
                        if (df[col] > 0).all():
                            try:
                                df[f'{col}_boxcox'], _ = boxcox(df[col])
                            except:
                                logger.warning(f"Box-Cox transformation failed for {col}")

    def _consolidate_data(self) -> None:
        """Consolidate all datasets into a single feature matrix"""
        logger.info("Consolidating data...")

        # Start with borrows as the base (since it has the most complete data)
        base_df = self.borrows_df.groupby('walletAddress').agg({
            'totalNumberOfBorrow': 'first',
            'totalAmountOfBorrowInUSD': 'first',
            'highestBorrowInUSD': 'first',
            'lowestBorrowInUSD': 'first',
            'averageBorrowInUSD': 'first',
            'timestamp': ['min', 'max', 'count'],
            'lendingPoolAddress': 'nunique',
            'tokenAddress': 'nunique'
        }).reset_index()

        # Flatten column names
        base_df.columns = ['walletAddress', 'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD',
                          'highestBorrowInUSD', 'lowestBorrowInUSD', 'averageBorrowInUSD',
                          'first_borrow_date', 'last_borrow_date', 'borrow_frequency',
                          'unique_lending_pools', 'unique_tokens']

        # Add deposits data
        deposits_agg = self.deposits_df.groupby('walletAddress').agg({
            'totalNumberOfDeposit': 'first',
            'totalAmountOfDepositInUSD': 'first',
            'highestDepositInUSD': 'first',
            'lowestDepositInUSD': 'first',
            'averageDepositInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(deposits_agg, on='walletAddress', how='left')

        # Add repays data
        repays_agg = self.repays_df.groupby('walletAddress').agg({
            'totalAmountOfRepayInUSD': 'first',
            'totalNumberOfRepay': 'first',
            'highestRepayInUSD': 'first',
            'lowestRepayInUSD': 'first',
            'averageRepayInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(repays_agg, on='walletAddress', how='left')

        # Add withdraws data
        withdraws_agg = self.withdraws_df.groupby('walletAddress').agg({
            'totalNumberOfWithdraw': 'first',
            'totalAmountOfWithdrawInUSD': 'first',
            'highestWithdrawInUSD': 'first',
            'lowestWithdrawInUSD': 'first',
            'averageWithdrawInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(withdraws_agg, on='walletAddress', how='left')

        # Add liquidation flag
        liquidated_wallets = set(self.liquidates_df['walletAddress'].unique()) if not self.liquidates_df.empty else set()
        base_df['has_been_liquidated'] = base_df['walletAddress'].isin(liquidated_wallets).astype(int)

        self.consolidated_df = base_df
        logger.info(f"Consolidated data shape: {self.consolidated_df.shape}")

    def engineer_features(self) -> None:
        """
        Engineer features for credit scoring following DeFi best practices
        Creates NFCS (Non-Fungible Credit Score) components
        """
        logger.info("Engineering features...")

        df = self.consolidated_df.copy()

        # Fill missing values with 0 for financial metrics
        financial_cols = [col for col in df.columns if 'USD' in col or 'Number' in col]
        df[financial_cols] = df[financial_cols].fillna(0)

        # 1. Repayment Behavior Features (Core NFCS component)
        df['repayment_ratio'] = np.where(df['totalAmountOfBorrowInUSD'] > 0,
                                        df['totalAmountOfRepayInUSD'] / df['totalAmountOfBorrowInUSD'], 0)
        df['repayment_consistency'] = np.where(df['totalNumberOfBorrow'] > 0,
                                              df['totalNumberOfRepay'] / df['totalNumberOfBorrow'], 0)

        # 2. Liquidity Management Features
        df['deposit_to_borrow_ratio'] = np.where(df['totalAmountOfBorrowInUSD'] > 0,
                                                 df['totalAmountOfDepositInUSD'] / df['totalAmountOfBorrowInUSD'], 0)
        df['withdraw_to_deposit_ratio'] = np.where(df['totalAmountOfDepositInUSD'] > 0,
                                                  df['totalAmountOfWithdrawInUSD'] / df['totalAmountOfDepositInUSD'], 0)

        # 3. Activity Diversity Features (Protocol interaction diversity)
        df['protocol_diversity_score'] = df['unique_lending_pools'] * df['unique_tokens']
        df['avg_transaction_size'] = (df['totalAmountOfBorrowInUSD'] + df['totalAmountOfDepositInUSD']) / \
                                    (df['totalNumberOfBorrow'] + df['totalNumberOfDeposit'] + 1)

        # 4. Risk Indicators
        df['liquidation_risk'] = df['has_been_liquidated']
        df['high_leverage_indicator'] = (df['totalAmountOfBorrowInUSD'] > df['totalAmountOfDepositInUSD']).astype(int)

        # 5. Temporal Features (Wallet age and activity patterns)
        df['first_borrow_date'] = pd.to_datetime(df['first_borrow_date'])
        df['last_borrow_date'] = pd.to_datetime(df['last_borrow_date'])
        df['wallet_age_days'] = (df['last_borrow_date'] - df['first_borrow_date']).dt.days
        df['activity_frequency'] = df['borrow_frequency'] / (df['wallet_age_days'] + 1)

        # 6. Composite NFCS Score Components
        # Normalize key metrics to 0-1 scale for NFCS calculation
        scaler_features = ['repayment_ratio', 'repayment_consistency', 'deposit_to_borrow_ratio',
                          'protocol_diversity_score', 'activity_frequency']

        for feature in scaler_features:
            if feature in df.columns:
                df[f'{feature}_normalized'] = (df[feature] - df[feature].min()) / (df[feature].max() - df[feature].min() + 1e-8)

        # Calculate preliminary NFCS (0-1000 scale)
        df['nfcs_score'] = (
            df['repayment_ratio_normalized'] * 0.3 +
            df['repayment_consistency_normalized'] * 0.25 +
            df['deposit_to_borrow_ratio_normalized'] * 0.2 +
            df['protocol_diversity_score_normalized'] * 0.15 +
            df['activity_frequency_normalized'] * 0.1
        ) * 1000

        # Penalize liquidated wallets
        df['nfcs_score'] = np.where(df['has_been_liquidated'] == 1,
                                   df['nfcs_score'] * 0.5, df['nfcs_score'])

        self.features_df = df
        logger.info(f"Feature engineering completed. Features shape: {self.features_df.shape}")

    def create_target_variable(self) -> None:
        """
        Create target variable for supervised learning
        Based on repayment behavior and liquidation history
        """
        logger.info("Creating target variable...")

        # Define credit risk categories based on DeFi lending criteria
        def categorize_risk(row):
            # High risk: liquidated or very poor repayment
            if row['has_been_liquidated'] == 1 or row['repayment_ratio'] < 0.5:
                return 2  # High risk
            # Medium risk: moderate repayment behavior
            elif row['repayment_ratio'] < 0.8 or row['repayment_consistency'] < 0.7:
                return 1  # Medium risk
            # Low risk: good repayment behavior
            else:
                return 0  # Low risk

        self.features_df['credit_risk'] = self.features_df.apply(categorize_risk, axis=1)

        # Also create binary classification (good vs bad borrower)
        self.features_df['is_good_borrower'] = (self.features_df['credit_risk'] == 0).astype(int)

        logger.info("Target variable created")
        logger.info(f"Credit risk distribution:\n{self.features_df['credit_risk'].value_counts()}")

    def visualize_data(self) -> None:
        """
        Create comprehensive data visualizations for EDA
        Following the guide's emphasis on understanding data distribution, outliers, and correlations
        """
        logger.info("Creating data visualizations...")

        # Set up the plotting environment
        plt.rcParams['figure.figsize'] = (15, 10)

        # 1. Distribution Analysis
        self._plot_distributions()

        # 2. Correlation Analysis
        self._plot_correlations()

        # 3. Outlier Analysis
        self._plot_outliers()

        # 4. Target Variable Analysis
        self._plot_target_analysis()

        # 5. Feature Importance Visualization (if models are trained)
        if self.models:
            self._plot_feature_importance()

        logger.info("Data visualization completed")

    def _plot_distributions(self) -> None:
        """Plot distribution of key features"""
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('Feature Distributions', fontsize=16, fontweight='bold')

        key_features = ['totalAmountOfBorrowInUSD', 'totalAmountOfDepositInUSD', 'repayment_ratio',
                       'deposit_to_borrow_ratio', 'nfcs_score', 'protocol_diversity_score',
                       'activity_frequency', 'wallet_age_days', 'repayment_consistency']

        for i, feature in enumerate(key_features):
            if feature in self.features_df.columns:
                row, col = i // 3, i % 3

                # Histogram with KDE
                self.features_df[feature].hist(bins=50, alpha=0.7, ax=axes[row, col])
                axes[row, col].set_title(f'{feature} Distribution')
                axes[row, col].set_xlabel(feature)
                axes[row, col].set_ylabel('Frequency')

                # Add statistics
                mean_val = self.features_df[feature].mean()
                median_val = self.features_df[feature].median()
                axes[row, col].axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.2f}')
                axes[row, col].axvline(median_val, color='green', linestyle='--', label=f'Median: {median_val:.2f}')
                axes[row, col].legend()

        plt.tight_layout()
        plt.savefig('feature_distributions.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_correlations(self) -> None:
        """Plot correlation matrix"""
        # Select numeric features for correlation
        numeric_features = self.features_df.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.features_df[numeric_features].corr()

        plt.figure(figsize=(16, 12))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_outliers(self) -> None:
        """Plot box plots to identify outliers"""
        key_features = ['totalAmountOfBorrowInUSD', 'totalAmountOfDepositInUSD', 'repayment_ratio',
                       'nfcs_score', 'protocol_diversity_score']

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Outlier Analysis - Box Plots', fontsize=16, fontweight='bold')

        for i, feature in enumerate(key_features):
            if feature in self.features_df.columns:
                row, col = i // 3, i % 3
                self.features_df.boxplot(column=feature, ax=axes[row, col])
                axes[row, col].set_title(f'{feature}')
                axes[row, col].tick_params(axis='x', rotation=45)

        # Remove empty subplot
        if len(key_features) < 6:
            fig.delaxes(axes[1, 2])

        plt.tight_layout()
        plt.savefig('outlier_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_target_analysis(self) -> None:
        """Analyze target variable distribution and relationships"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Target Variable Analysis', fontsize=16, fontweight='bold')

        # Credit risk distribution
        self.features_df['credit_risk'].value_counts().plot(kind='bar', ax=axes[0, 0])
        axes[0, 0].set_title('Credit Risk Distribution')
        axes[0, 0].set_xlabel('Risk Level (0=Low, 1=Medium, 2=High)')
        axes[0, 0].set_ylabel('Count')

        # NFCS score by risk level
        self.features_df.boxplot(column='nfcs_score', by='credit_risk', ax=axes[0, 1])
        axes[0, 1].set_title('NFCS Score by Risk Level')

        # Repayment ratio by risk level
        self.features_df.boxplot(column='repayment_ratio', by='credit_risk', ax=axes[1, 0])
        axes[1, 0].set_title('Repayment Ratio by Risk Level')

        # Liquidation vs NFCS score
        sns.scatterplot(data=self.features_df, x='nfcs_score', y='repayment_ratio',
                       hue='has_been_liquidated', ax=axes[1, 1])
        axes[1, 1].set_title('NFCS Score vs Repayment Ratio (Liquidation Status)')

        plt.tight_layout()
        plt.savefig('target_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def train_models(self) -> None:
        """
        Train multiple ML models following the ensemble approach from the guide
        """
        logger.info("Training ML models...")

        # Prepare features and target
        feature_cols = [col for col in self.features_df.columns
                       if col not in ['walletAddress', 'credit_risk', 'is_good_borrower',
                                     'first_borrow_date', 'last_borrow_date']]

        X = self.features_df[feature_cols].fillna(0)
        y_multiclass = self.features_df['credit_risk']
        y_binary = self.features_df['is_good_borrower']

        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=feature_cols)

        # Split data
        X_train, X_test, y_train_multi, y_test_multi = train_test_split(
            X_scaled_df, y_multiclass, test_size=0.2, random_state=42, stratify=y_multiclass)

        X_train_bin, X_test_bin, y_train_bin, y_test_bin = train_test_split(
            X_scaled_df, y_binary, test_size=0.2, random_state=42, stratify=y_binary)

        # 1. Random Forest (Robust for high-dimensional data)
        logger.info("Training Random Forest...")
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf_model.fit(X_train, y_train_multi)
        self.models['random_forest'] = rf_model
        self.feature_importance['random_forest'] = dict(zip(feature_cols, rf_model.feature_importances_))

        # 2. XGBoost (High performance for complex patterns)
        logger.info("Training XGBoost...")
        xgb_model = xgb.XGBClassifier(random_state=42, eval_metric='mlogloss')
        xgb_model.fit(X_train, y_train_multi)
        self.models['xgboost'] = xgb_model
        self.feature_importance['xgboost'] = dict(zip(feature_cols, xgb_model.feature_importances_))

        # 3. Isolation Forest for Anomaly Detection (Bot detection)
        logger.info("Training Isolation Forest for anomaly detection...")
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        iso_forest.fit(X_train)
        self.models['isolation_forest'] = iso_forest

        # 4. K-means Clustering for new users
        logger.info("Training K-means for user clustering...")
        kmeans = KMeans(n_clusters=5, random_state=42)
        kmeans.fit(X_train)
        self.models['kmeans'] = kmeans

        # Store training data for evaluation
        self.X_train, self.X_test = X_train, X_test
        self.y_train_multi, self.y_test_multi = y_train_multi, y_test_multi
        self.y_train_bin, self.y_test_bin = y_train_bin, y_test_bin
        self.feature_cols = feature_cols

        logger.info("Model training completed")

    def evaluate_models(self) -> Dict:
        """Evaluate trained models"""
        logger.info("Evaluating models...")

        evaluation_results = {}

        # Evaluate Random Forest
        rf_pred = self.models['random_forest'].predict(self.X_test)
        rf_prob = self.models['random_forest'].predict_proba(self.X_test)

        evaluation_results['random_forest'] = {
            'classification_report': classification_report(self.y_test_multi, rf_pred),
            'confusion_matrix': confusion_matrix(self.y_test_multi, rf_pred),
            'accuracy': (rf_pred == self.y_test_multi).mean()
        }

        # Evaluate XGBoost
        xgb_pred = self.models['xgboost'].predict(self.X_test)
        xgb_prob = self.models['xgboost'].predict_proba(self.X_test)

        evaluation_results['xgboost'] = {
            'classification_report': classification_report(self.y_test_multi, xgb_pred),
            'confusion_matrix': confusion_matrix(self.y_test_multi, xgb_pred),
            'accuracy': (xgb_pred == self.y_test_multi).mean()
        }

        # Anomaly detection evaluation
        anomaly_pred = self.models['isolation_forest'].predict(self.X_test)
        anomaly_scores = self.models['isolation_forest'].decision_function(self.X_test)

        evaluation_results['isolation_forest'] = {
            'anomaly_ratio': (anomaly_pred == -1).mean(),
            'anomaly_scores_stats': {
                'mean': anomaly_scores.mean(),
                'std': anomaly_scores.std(),
                'min': anomaly_scores.min(),
                'max': anomaly_scores.max()
            }
        }

        # Clustering evaluation
        cluster_labels = self.models['kmeans'].predict(self.X_test)
        evaluation_results['kmeans'] = {
            'cluster_distribution': pd.Series(cluster_labels).value_counts().to_dict(),
            'inertia': self.models['kmeans'].inertia_
        }

        self.evaluation_results = evaluation_results
        logger.info("Model evaluation completed")

        return evaluation_results

    def _plot_feature_importance(self) -> None:
        """Plot feature importance for tree-based models"""
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle('Feature Importance Analysis', fontsize=16, fontweight='bold')

        # Random Forest feature importance
        rf_importance = pd.DataFrame(list(self.feature_importance['random_forest'].items()),
                                   columns=['feature', 'importance']).sort_values('importance', ascending=True)

        rf_importance.tail(15).plot(x='feature', y='importance', kind='barh', ax=axes[0])
        axes[0].set_title('Random Forest - Top 15 Features')
        axes[0].set_xlabel('Importance')

        # XGBoost feature importance
        xgb_importance = pd.DataFrame(list(self.feature_importance['xgboost'].items()),
                                    columns=['feature', 'importance']).sort_values('importance', ascending=True)

        xgb_importance.tail(15).plot(x='feature', y='importance', kind='barh', ax=axes[1])
        axes[1].set_title('XGBoost - Top 15 Features')
        axes[1].set_xlabel('Importance')

        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

    def calculate_nfcs_score(self, wallet_address: str) -> Dict:
        """
        Calculate NFCS (Non-Fungible Credit Score) for a specific wallet
        Returns comprehensive scoring information
        """
        if wallet_address not in self.features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in dataset'}

        wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address].iloc[0]

        # Get model predictions
        feature_vector = wallet_data[self.feature_cols].fillna(0).values.reshape(1, -1)
        feature_vector_scaled = self.scaler.transform(feature_vector)

        # Random Forest prediction
        rf_risk_pred = self.models['random_forest'].predict(feature_vector_scaled)[0]
        rf_risk_prob = self.models['random_forest'].predict_proba(feature_vector_scaled)[0]

        # XGBoost prediction
        xgb_risk_pred = self.models['xgboost'].predict(feature_vector_scaled)[0]
        xgb_risk_prob = self.models['xgboost'].predict_proba(feature_vector_scaled)[0]

        # Anomaly detection
        anomaly_score = self.models['isolation_forest'].decision_function(feature_vector_scaled)[0]
        is_anomaly = self.models['isolation_forest'].predict(feature_vector_scaled)[0] == -1

        # Cluster assignment
        cluster = self.models['kmeans'].predict(feature_vector_scaled)[0]

        # Ensemble prediction (weighted average)
        ensemble_prob = (rf_risk_prob + xgb_risk_prob) / 2
        ensemble_risk = np.argmax(ensemble_prob)

        # Calculate final NFCS score (0-1000 scale)
        base_nfcs = wallet_data['nfcs_score']

        # Adjust based on model predictions
        risk_adjustment = {0: 1.0, 1: 0.7, 2: 0.3}  # Low, Medium, High risk
        model_adjusted_nfcs = base_nfcs * risk_adjustment[ensemble_risk]

        # Anomaly penalty
        if is_anomaly:
            model_adjusted_nfcs *= 0.5

        # Final score
        final_nfcs = max(0, min(1000, model_adjusted_nfcs))

        return {
            'wallet_address': wallet_address,
            'nfcs_score': final_nfcs,
            'risk_level': ['Low', 'Medium', 'High'][ensemble_risk],
            'risk_probabilities': {
                'low': ensemble_prob[0],
                'medium': ensemble_prob[1],
                'high': ensemble_prob[2]
            },
            'model_predictions': {
                'random_forest': {
                    'risk_level': ['Low', 'Medium', 'High'][rf_risk_pred],
                    'probabilities': rf_risk_prob.tolist()
                },
                'xgboost': {
                    'risk_level': ['Low', 'Medium', 'High'][xgb_risk_pred],
                    'probabilities': xgb_risk_prob.tolist()
                }
            },
            'anomaly_detection': {
                'is_anomaly': is_anomaly,
                'anomaly_score': anomaly_score
            },
            'cluster': int(cluster),
            'key_metrics': {
                'repayment_ratio': wallet_data['repayment_ratio'],
                'repayment_consistency': wallet_data['repayment_consistency'],
                'deposit_to_borrow_ratio': wallet_data['deposit_to_borrow_ratio'],
                'protocol_diversity_score': wallet_data['protocol_diversity_score'],
                'has_been_liquidated': bool(wallet_data['has_been_liquidated']),
                'total_borrow_amount': wallet_data['totalAmountOfBorrowInUSD'],
                'total_repay_amount': wallet_data['totalAmountOfRepayInUSD']
            }
        }

    def run_full_pipeline(self) -> None:
        """Run the complete ML pipeline"""
        logger.info("Starting full DeFi Credit Scoring Pipeline...")

        # 1. Load and preprocess data
        self.load_data()
        self.preprocess_data()

        # 2. Feature engineering
        self.engineer_features()
        self.create_target_variable()

        # 3. Data visualization
        self.visualize_data()

        # 4. Train models
        self.train_models()

        # 5. Evaluate models
        evaluation_results = self.evaluate_models()

        # 6. Print summary
        self._print_pipeline_summary(evaluation_results)

        logger.info("Pipeline completed successfully!")

    def _print_pipeline_summary(self, evaluation_results: Dict) -> None:
        """Print a summary of the pipeline results"""
        print("\n" + "="*80)
        print("DEFI CREDIT SCORING PIPELINE SUMMARY")
        print("="*80)

        print(f"\nDataset Summary:")
        print(f"- Total wallets analyzed: {len(self.features_df)}")
        print(f"- Features engineered: {len(self.feature_cols)}")
        print(f"- Risk distribution: {dict(self.features_df['credit_risk'].value_counts())}")

        print(f"\nModel Performance:")
        for model_name, results in evaluation_results.items():
            if 'accuracy' in results:
                print(f"- {model_name.title()}: {results['accuracy']:.3f} accuracy")

        print(f"\nTop 5 Most Important Features:")
        if 'random_forest' in self.feature_importance:
            top_features = sorted(self.feature_importance['random_forest'].items(),
                                key=lambda x: x[1], reverse=True)[:5]
            for i, (feature, importance) in enumerate(top_features, 1):
                print(f"{i}. {feature}: {importance:.3f}")

        print(f"\nNFCS Score Statistics:")
        print(f"- Mean NFCS: {self.features_df['nfcs_score'].mean():.2f}")
        print(f"- Median NFCS: {self.features_df['nfcs_score'].median():.2f}")
        print(f"- Std NFCS: {self.features_df['nfcs_score'].std():.2f}")

        print("\n" + "="*80)

# Example usage and testing
if __name__ == "__main__":
    # Initialize pipeline
    pipeline = DeFiCreditScoringPipeline(".")

    # Run full pipeline
    pipeline.run_full_pipeline()

    # Example: Calculate NFCS for a specific wallet
    # Replace with an actual wallet address from your data
    sample_wallets = pipeline.features_df['walletAddress'].head(3).tolist()

    print("\nSample NFCS Calculations:")
    print("-" * 50)
    for wallet in sample_wallets:
        score_info = pipeline.calculate_nfcs_score(wallet)
        if 'error' not in score_info:
            print(f"\nWallet: {wallet[:10]}...")
            print(f"NFCS Score: {score_info['nfcs_score']:.2f}")
            print(f"Risk Level: {score_info['risk_level']}")
            print(f"Repayment Ratio: {score_info['key_metrics']['repayment_ratio']:.3f}")
        else:
            print(f"Error for wallet {wallet}: {score_info['error']}")
