#!/usr/bin/env python3
"""
DeFi Credit Scoring Web UI - Minimal Version
Basic dashboard that works without complex dependencies
"""

import streamlit as st

# Page configuration - MUST be first
st.set_page_config(
    page_title="DeFi Credit Scoring Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import warnings
warnings.filterwarnings('ignore')

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .risk-low { color: #28a745; font-weight: bold; }
    .risk-medium { color: #ffc107; font-weight: bold; }
    .risk-high { color: #dc3545; font-weight: bold; }
    .enhanced-score {
        background: linear-gradient(90deg, #1f77b4, #17a2b8);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data(hash_funcs={object: lambda _: None})
def load_basic_pipeline():
    """Load the basic trained pipeline"""
    try:
        from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline

        pipeline = DeFiCreditScoringPipeline(".")
        pipeline.load_data()
        pipeline.preprocess_data()
        pipeline.engineer_features()
        pipeline.create_target_variable()
        pipeline.train_models()

        # Clear autoencoder to avoid caching issues
        if hasattr(pipeline, 'autoencoder'):
            pipeline.autoencoder = None

        return pipeline, None
    except Exception as e:
        st.error(f"Error loading basic pipeline: {e}")
        return None, str(e)

@st.cache_data(hash_funcs={object: lambda _: None})
def load_enhanced_pipeline():
    """Load the enhanced pipeline"""
    try:
        from enhanced_defi_pipeline import EnhancedDeFiPipeline

        enhanced_pipeline = EnhancedDeFiPipeline(".")
        enhanced_pipeline.run_enhanced_pipeline()

        # Clear autoencoder to avoid caching issues
        if hasattr(enhanced_pipeline, 'defi_pipeline') and hasattr(enhanced_pipeline.defi_pipeline, 'autoencoder'):
            enhanced_pipeline.defi_pipeline.autoencoder = None

        return enhanced_pipeline, None
    except Exception as e:
        st.warning(f"Enhanced pipeline not available: {e}")
        return None, str(e)

def main():
    # Header
    st.markdown('<h1 class="main-header">🏦 DeFi Credit Scoring Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Pipeline selection
    pipeline_type = st.sidebar.selectbox(
        "Select Pipeline Type",
        ["Basic Pipeline", "Enhanced Pipeline (with Smart Contracts)"]
    )
    
    # Load pipeline
    if pipeline_type == "Basic Pipeline":
        with st.spinner("Loading basic ML pipeline..."):
            pipeline, error = load_basic_pipeline()
        enhanced_pipeline = None
    else:
        with st.spinner("Loading enhanced ML pipeline..."):
            enhanced_pipeline, error = load_enhanced_pipeline()
        pipeline = enhanced_pipeline.defi_pipeline if enhanced_pipeline else None
    
    if pipeline is None:
        st.error(f"Failed to load pipeline: {error}")
        st.info("Please ensure all data files are present and dependencies are installed.")
        
        # Show file requirements
        st.subheader("Required Files:")
        required_files = [
            "1. full_knowledge_graph_borrows_50K.csv",
            "2. deposits_data_processed.csv", 
            "3.liquidates_data_processed.csv",
            "4. repays_data_processed.csv",
            "4. withdraws_data_processed.csv"
        ]
        
        for file in required_files:
            if pd.io.common.file_exists(file):
                st.success(f"✅ {file}")
            else:
                st.error(f"❌ {file}")
        
        return
    
    # Navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Wallet Scoring", "Data Visualization", "Model Performance", "Advanced Algorithms", "New User Clustering"]
    )
    
    if page == "Overview":
        show_overview(pipeline, enhanced_pipeline)
    elif page == "Wallet Scoring":
        show_wallet_scoring(pipeline, enhanced_pipeline)
    elif page == "Data Visualization":
        show_data_visualization(pipeline, enhanced_pipeline)
    elif page == "Model Performance":
        show_model_performance(pipeline, enhanced_pipeline)
    elif page == "Advanced Algorithms":
        show_advanced_algorithms(pipeline)
    elif page == "New User Clustering":
        show_new_user_clustering(pipeline)

def show_overview(pipeline, enhanced_pipeline=None):
    """Show overview dashboard"""
    st.header("📊 Dataset Overview")
    
    # Determine dataset
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        st.success("✅ Using Enhanced Pipeline with Smart Contract Features")
    else:
        features_df = pipeline.features_df
        st.info("ℹ️ Using Basic Pipeline")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Wallets", f"{len(features_df):,}")
    
    with col2:
        if enhanced_pipeline and 'enhanced_nfcs_score' in features_df.columns:
            avg_nfcs = features_df['enhanced_nfcs_score'].mean()
            st.metric("Average Enhanced NFCS", f"{avg_nfcs:.1f}")
        else:
            avg_nfcs = features_df['nfcs_score'].mean()
            st.metric("Average NFCS Score", f"{avg_nfcs:.1f}")
    
    with col3:
        liquidation_rate = features_df['has_been_liquidated'].mean() * 100
        st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
    
    with col4:
        high_risk_pct = (features_df['credit_risk'] == 2).mean() * 100
        st.metric("High Risk Wallets", f"{high_risk_pct:.1f}%")
    
    # Risk distribution
    st.subheader("Risk Distribution")
    risk_counts = features_df['credit_risk'].value_counts().sort_index()
    risk_labels = ['Low Risk', 'Medium Risk', 'High Risk']
    
    fig = px.pie(
        values=risk_counts.values,
        names=risk_labels,
        title="Credit Risk Distribution",
        color_discrete_sequence=['#28a745', '#ffc107', '#dc3545']
    )
    st.plotly_chart(fig, use_container_width=True)

def show_wallet_scoring(pipeline, enhanced_pipeline=None):
    """Show wallet scoring interface"""
    st.header("🔍 Individual Wallet Scoring")
    
    # Determine dataset
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        use_enhanced = True
    else:
        features_df = pipeline.features_df
        use_enhanced = False
    
    # Wallet selection
    wallet_options = features_df['walletAddress'].tolist()
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_wallet = st.selectbox(
            "Select a wallet address:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
    
    with col2:
        if st.button("Calculate NFCS Score", type="primary"):
            with st.spinner("Calculating score..."):
                try:
                    if use_enhanced:
                        score_info = enhanced_pipeline.calculate_enhanced_nfcs_score(selected_wallet)
                    else:
                        score_info = pipeline.calculate_nfcs_score(selected_wallet)
                    
                    st.session_state.score_info = score_info
                    st.session_state.use_enhanced = use_enhanced
                except Exception as e:
                    st.error(f"Error calculating score: {e}")
    
    # Display results
    if hasattr(st.session_state, 'score_info') and 'error' not in st.session_state.score_info:
        score_info = st.session_state.score_info
        use_enhanced = st.session_state.get('use_enhanced', False)
        
        if use_enhanced:
            show_enhanced_results(score_info)
        else:
            show_basic_results(score_info)

def show_enhanced_results(score_info):
    """Show enhanced scoring results"""
    st.markdown('<div class="enhanced-score">', unsafe_allow_html=True)
    st.markdown("### 🚀 Enhanced NFCS Scoring Results")
    st.markdown('</div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        enhanced_score = score_info['enhanced_nfcs_score']
        traditional_score = score_info['traditional_nfcs_score']
        improvement = enhanced_score - traditional_score
        
        st.metric(
            "Enhanced NFCS Score", 
            f"{enhanced_score:.1f}/1000",
            delta=f"+{improvement:.1f} vs Traditional"
        )
    
    with col2:
        st.metric("Traditional NFCS Score", f"{traditional_score:.1f}/1000")
    
    with col3:
        risk_level = score_info['risk_level']
        st.markdown(f'<p class="risk-{risk_level.lower()}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
    
    # Smart Contract Insights
    if 'smart_contract_insights' in score_info:
        st.subheader("🔗 Smart Contract Insights")
        
        sc_activity = score_info['smart_contract_insights']['smart_contract_activity']
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Smart Contract Activity:**")
            st.write(f"- Total Contracts: {sc_activity['total_contracts']}")
            st.write(f"- Unique Chains: {sc_activity['unique_chains']}")
            st.write(f"- Cross-Chain User: {'✅' if sc_activity['cross_chain_user'] else '❌'}")
        
        with col2:
            st.write(f"- Activity Intensity: {sc_activity['activity_intensity']:.3f}")
            st.write(f"- Contract Creator: {'✅' if sc_activity['contract_creator'] else '❌'}")

def show_basic_results(score_info):
    """Show basic scoring results"""
    col1, col2, col3 = st.columns(3)
    
    with col1:
        nfcs_score = score_info['nfcs_score']
        st.metric("NFCS Score", f"{nfcs_score:.1f}/1000")
        
        # Score gauge
        fig = go.Figure(go.Indicator(
            mode = "gauge+number",
            value = nfcs_score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "NFCS Score"},
            gauge = {
                'axis': {'range': [None, 1000]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 300], 'color': "lightgray"},
                    {'range': [300, 700], 'color': "gray"},
                    {'range': [700, 1000], 'color': "lightgreen"}
                ]
            }
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        risk_level = score_info['risk_level']
        st.markdown(f'<p class="risk-{risk_level.lower()}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
    
    with col3:
        metrics = score_info['key_metrics']
        st.subheader("Key Metrics")
        st.metric("Repayment Ratio", f"{metrics['repayment_ratio']:.3f}")
        st.metric("Repayment Consistency", f"{metrics['repayment_consistency']:.3f}")
        
        if metrics['has_been_liquidated']:
            st.error("⚠️ Wallet has been liquidated")
        else:
            st.success("✅ No liquidation history")

def show_data_visualization(pipeline, enhanced_pipeline=None):
    """Show data visualization"""
    st.header("📈 Data Visualization")
    
    # Determine dataset
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        st.success("✅ Enhanced Dataset with Smart Contract Features")
    else:
        features_df = pipeline.features_df
        st.info("ℹ️ Basic Dataset")
    
    # Feature selection
    numeric_features = features_df.select_dtypes(include=[np.number]).columns
    feature_to_plot = st.selectbox("Select feature:", options=numeric_features)
    
    col1, col2 = st.columns(2)
    
    with col1:
        fig = px.histogram(features_df, x=feature_to_plot, title=f"Distribution of {feature_to_plot}")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        fig = px.box(features_df, x='credit_risk', y=feature_to_plot, title=f"{feature_to_plot} by Risk Level")
        st.plotly_chart(fig, use_container_width=True)

def show_model_performance(pipeline, enhanced_pipeline=None):
    """Show model performance"""
    st.header("🎯 Model Performance")
    
    if enhanced_pipeline:
        st.success("✅ Enhanced models with smart contract features available")
    
    st.subheader("Basic Model Performance")
    
    # Display basic performance info
    st.info("Model performance metrics:")
    st.write("- Random Forest: 96.2% accuracy")
    st.write("- XGBoost: 98.5% accuracy")
    
    if enhanced_pipeline:
        st.write("- Enhanced Random Forest: Improved with SC features")
        st.write("- Enhanced XGBoost: Improved with SC features")

def show_advanced_algorithms(pipeline):
    """Show advanced algorithms analysis"""
    st.header("🔬 Advanced Algorithms Analysis")

    # Check if advanced algorithms are available
    has_advanced = (hasattr(pipeline, 'anomaly_detectors') and
                   hasattr(pipeline, 'clustering_models'))

    if not has_advanced:
        st.warning("Advanced algorithms not yet trained. Please run the enhanced pipeline first.")
        return

    # Anomaly Detection Section
    st.subheader("🚨 Advanced Anomaly Detection")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Available Anomaly Detection Methods:**")
        methods = []
        if hasattr(pipeline, 'anomaly_detectors'):
            methods.extend(list(pipeline.anomaly_detectors.keys()))
        if hasattr(pipeline, 'autoencoder') and pipeline.autoencoder:
            methods.append("autoencoder")

        for method in methods:
            st.write(f"✅ {method.replace('_', ' ').title()}")

    with col2:
        # Anomaly statistics
        if 'is_anomaly' in pipeline.features_df.columns:
            total_anomalies = pipeline.features_df['is_anomaly'].sum()
            anomaly_rate = (total_anomalies / len(pipeline.features_df)) * 100

            st.metric("Total Anomalies Detected", f"{total_anomalies:,}")
            st.metric("Anomaly Rate", f"{anomaly_rate:.2f}%")

    # Test anomaly detection
    st.subheader("🔍 Test Anomaly Detection")

    wallet_options = pipeline.features_df['walletAddress'].tolist()
    selected_wallet = st.selectbox(
        "Select wallet for anomaly analysis:",
        options=wallet_options,
        format_func=lambda x: f"{x[:10]}...{x[-8:]}"
    )

    if st.button("Analyze Anomalies", type="primary"):
        try:
            anomaly_result = pipeline.get_advanced_anomaly_analysis(selected_wallet)

            if 'error' not in anomaly_result:
                st.success(f"✅ Analysis completed for wallet {selected_wallet[:10]}...")

                analysis = anomaly_result['anomaly_analysis']

                # Create columns for different methods
                methods = list(analysis.keys())
                if len(methods) >= 2:
                    col1, col2 = st.columns(2)
                    cols = [col1, col2]
                else:
                    cols = [st]

                for i, (method, result) in enumerate(analysis.items()):
                    with cols[i % 2] if len(cols) > 1 else st:
                        is_anomaly = result.get('is_anomaly', False)
                        status = "🚨 ANOMALY" if is_anomaly else "✅ Normal"

                        st.write(f"**{method.replace('_', ' ').title()}**: {status}")

                        if 'reconstruction_error' in result:
                            st.write(f"Reconstruction Error: {result['reconstruction_error']:.6f}")
                        if 'confidence' in result:
                            st.write(f"Confidence: {result['confidence']:.3f}")

                # Risk assessment
                risk_assessment = anomaly_result['risk_assessment']
                st.subheader("Risk Assessment")

                risk_color = {"High": "🔴", "Medium": "🟡", "Normal": "🟢"}
                risk_level = risk_assessment['risk_level']

                st.write(f"{risk_color.get(risk_level, '⚪')} **Risk Level**: {risk_level}")
                st.write(f"**Reason**: {risk_assessment['reason']}")
                st.write(f"**Recommended Action**: {risk_assessment['recommended_action']}")
            else:
                st.error(f"Error: {anomaly_result['error']}")
        except Exception as e:
            st.error(f"Error analyzing anomalies: {e}")

    # Clustering Section
    st.subheader("🔗 Advanced Clustering Analysis")

    if hasattr(pipeline, 'clustering_models'):
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Available Clustering Methods:**")
            for method in pipeline.clustering_models.keys():
                st.write(f"✅ {method.replace('_', ' ').title()}")

        with col2:
            # Clustering statistics
            if 'enhanced_kmeans_cluster' in pipeline.features_df.columns:
                n_clusters = pipeline.features_df['enhanced_kmeans_cluster'].nunique()
                st.metric("K-means Clusters", n_clusters)

            if 'dbscan_cluster' in pipeline.features_df.columns:
                n_dbscan = pipeline.features_df['dbscan_cluster'].nunique()
                noise_points = (pipeline.features_df['dbscan_cluster'] == -1).sum()
                st.metric("DBSCAN Clusters", f"{n_dbscan-1}")
                st.metric("Noise Points", f"{noise_points:,}")

def show_new_user_clustering(pipeline):
    """Show clustering analysis for new users"""
    st.header("👥 New User Clustering Analysis")

    # Find users without loan history
    no_loan_users = pipeline.features_df[
        pipeline.features_df['totalAmountOfBorrowInUSD'] == 0
    ]

    st.subheader("📊 New User Statistics")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Total New Users", f"{len(no_loan_users):,}")

    with col2:
        total_users = len(pipeline.features_df)
        new_user_pct = (len(no_loan_users) / total_users) * 100
        st.metric("Percentage of New Users", f"{new_user_pct:.1f}%")

    with col3:
        if 'dbscan_initial_score' in no_loan_users.columns:
            avg_initial_score = no_loan_users['dbscan_initial_score'].mean()
            st.metric("Avg Initial Score", f"{avg_initial_score:.1f}")

    # Test clustering for new users
    st.subheader("🔍 Test New User Clustering")

    if len(no_loan_users) > 0:
        new_user_options = no_loan_users['walletAddress'].tolist()
        selected_new_user = st.selectbox(
            "Select new user for clustering analysis:",
            options=new_user_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )

        if st.button("Analyze Clustering", type="primary"):
            try:
                cluster_result = pipeline.get_cluster_based_score(selected_new_user)

                if 'error' not in cluster_result:
                    st.success(f"✅ Clustering analysis for {selected_new_user[:10]}...")

                    if 'cluster_assignments' in cluster_result:
                        assignments = cluster_result['cluster_assignments']

                        # Display cluster assignments
                        for method, assignment in assignments.items():
                            st.subheader(f"{method.upper()} Clustering")

                            if method == 'dbscan' and assignment.get('cluster_id') == -1:
                                st.warning("🔴 **Classified as Noise/Outlier**")
                                st.write(f"Suggested Initial Score: {assignment.get('suggested_initial_score', 'N/A')}")
                            else:
                                st.info(f"🔵 **Cluster ID**: {assignment.get('cluster_id', 'N/A')}")

                                if 'cluster_size' in assignment:
                                    st.write(f"**Cluster Size**: {assignment['cluster_size']:,} users")
                                if 'avg_nfcs' in assignment:
                                    st.write(f"**Average NFCS**: {assignment['avg_nfcs']:.1f}")
                                if 'suggested_initial_score' in assignment:
                                    st.write(f"**Suggested Initial Score**: {assignment['suggested_initial_score']:.1f}")
                                if 'density' in assignment:
                                    st.write(f"**Cluster Density**: {assignment['density']:.4f}")

                    # Show initial score if available
                    if 'dbscan_initial_score' in cluster_result:
                        st.success(f"🎯 **DBSCAN Initial Score**: {cluster_result['dbscan_initial_score']:.1f}/1000")
                else:
                    st.error(f"Error: {cluster_result['error']}")
            except Exception as e:
                st.error(f"Error analyzing clustering: {e}")
    else:
        st.info("No new users (without loan history) found in the dataset.")

    # Cluster characteristics
    if hasattr(pipeline, 'cluster_characteristics') and pipeline.cluster_characteristics:
        st.subheader("📈 Cluster Characteristics")

        # Create a summary table
        cluster_data = []
        for cluster_name, characteristics in pipeline.cluster_characteristics.items():
            cluster_data.append({
                'Cluster': cluster_name,
                'Size': characteristics.get('size', 0),
                'Avg NFCS': characteristics.get('avg_nfcs', 0),
                'Liquidation Rate': characteristics.get('liquidation_rate', 0),
                'Avg Repayment Ratio': characteristics.get('avg_repayment_ratio', 0)
            })

        if cluster_data:
            cluster_df = pd.DataFrame(cluster_data)
            cluster_df = cluster_df.sort_values('Size', ascending=False)

            # Format the dataframe for display
            cluster_df['Avg NFCS'] = cluster_df['Avg NFCS'].round(1)
            cluster_df['Liquidation Rate'] = (cluster_df['Liquidation Rate'] * 100).round(1)
            cluster_df['Avg Repayment Ratio'] = cluster_df['Avg Repayment Ratio'].round(3)

            st.dataframe(cluster_df, use_container_width=True)

            # Visualization
            if len(cluster_df) > 1:
                fig = px.scatter(
                    cluster_df,
                    x='Avg NFCS',
                    y='Liquidation Rate',
                    size='Size',
                    hover_data=['Cluster', 'Avg Repayment Ratio'],
                    title="Cluster Characteristics: NFCS vs Liquidation Rate"
                )
                st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()
