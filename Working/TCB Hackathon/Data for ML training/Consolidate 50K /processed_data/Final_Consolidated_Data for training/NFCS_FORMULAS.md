# NFCS (Non-Fungible Credit Score) - Mathematical Formulas

## 🎯 Overview

The NFCS (Non-Fungible Credit Score) is a comprehensive credit scoring system designed for DeFi lending protocols. It combines multiple behavioral and financial metrics to create a dynamic, blockchain-based credit score ranging from 0 to 1000.

**Latest Update**: Enhanced NFCS now includes smart contract interaction patterns and cross-chain activity analysis for more accurate credit assessment.

## 📊 NFCS Formula Evolution

### Traditional NFCS Formula (Legacy)

```
Traditional_NFCS = (
    Repayment_Ratio_Normalized × 0.30 +
    Repayment_Consistency_Normalized × 0.25 +
    Deposit_Borrow_Ratio_Normalized × 0.20 +
    Protocol_Diversity_Normalized × 0.15 +
    Activity_Frequency_Normalized × 0.10
) × 1000
```

### Enhanced NFCS Formula (Current - v2.0)

```
Enhanced_NFCS = (
    # Traditional DeFi Components (70% weight)
    Repayment_Ratio_Normalized × 0.25 +
    Repayment_Consistency_Normalized × 0.20 +
    Deposit_Borrow_Ratio_Normalized × 0.15 +
    Protocol_Diversity_Normalized × 0.10 +

    # Smart Contract Components (30% weight)
    SC_Activity_Intensity_Normalized × 0.10 +
    SC_Cross_Chain_Ratio_Normalized × 0.05 +
    Sophisticated_User_Score_Normalized × 0.10 +
    Activity_Consistency_Score_Normalized × 0.05
) × 1000
```

### Final NFCS with Enhanced Adjustments

```
NFCS_final = Enhanced_NFCS × Liquidation_Penalty × Anomaly_Penalty × SC_Activity_Bonus × Dormancy_Penalty
```

Where:
- `Liquidation_Penalty = 0.5` if wallet has been liquidated, else `1.0`
- `Anomaly_Penalty = 0.5` if flagged as anomalous, else `1.0`
- `SC_Activity_Bonus = 1.1` if high smart contract activity, else `1.0`
- `Dormancy_Penalty = 0.8` if wallet is dormant, else `1.0`

## 🔢 Component Formulas

### Traditional DeFi Components (70% weight in Enhanced NFCS)

#### 1. Repayment Ratio (25% weight - reduced from 30%)

```
Repayment_Ratio = Total_Amount_Repaid_USD / Total_Amount_Borrowed_USD

Where:
- Total_Amount_Repaid_USD = Σ(all repayment transactions in USD)
- Total_Amount_Borrowed_USD = Σ(all borrow transactions in USD)
- Range: [0, ∞], typically [0, 1.5]
- Perfect repayment = 1.0
```

**Normalization:**
```
Repayment_Ratio_Normalized = (Repayment_Ratio - Min_Value) / (Max_Value - Min_Value + ε)

Where:
- Min_Value = minimum repayment ratio in dataset
- Max_Value = maximum repayment ratio in dataset  
- ε = 1e-8 (small constant to avoid division by zero)
```

#### 2. Repayment Consistency (20% weight - reduced from 25%)

```
Repayment_Consistency = Total_Number_of_Repays / Total_Number_of_Borrows

Where:
- Total_Number_of_Repays = count of repayment transactions
- Total_Number_of_Borrows = count of borrow transactions
- Range: [0, ∞], typically [0, 1.2]
- Perfect consistency = 1.0
```

**Interpretation:**
- `< 1.0`: Some borrows not repaid
- `= 1.0`: Each borrow has corresponding repay
- `> 1.0`: Multiple repays per borrow (partial repayments)

#### 3. Deposit to Borrow Ratio (15% weight - reduced from 20%)

```
Deposit_Borrow_Ratio = Total_Amount_Deposited_USD / Total_Amount_Borrowed_USD

Where:
- Total_Amount_Deposited_USD = Σ(all deposit transactions in USD)
- Total_Amount_Borrowed_USD = Σ(all borrow transactions in USD)
- Range: [0, ∞]
- Higher values indicate better collateralization
```

**Risk Interpretation:**
- `< 1.0`: Under-collateralized position
- `= 1.0`: Fully collateralized
- `> 1.0`: Over-collateralized (lower risk)

#### 4. Protocol Diversity Score (10% weight - reduced from 15%)

```
Protocol_Diversity_Score = Unique_Lending_Pools × Unique_Tokens

Where:
- Unique_Lending_Pools = count of distinct lending pool addresses
- Unique_Tokens = count of distinct token addresses
- Range: [1, ∞]
- Higher values indicate diversified DeFi activity
```

**Enhanced Formula (optional):**
```
Protocol_Diversity_Enhanced = log(1 + Unique_Lending_Pools) × log(1 + Unique_Tokens) × Interaction_Factor

Where:
- Interaction_Factor = 1 + (Cross_Chain_Activity × 0.1)
- Cross_Chain_Activity = number of different blockchains used
```

### Smart Contract Components (30% weight in Enhanced NFCS)

#### 5. Smart Contract Activity Intensity (10% weight - NEW)

```
SC_Activity_Intensity = SC_Total_Last_Day_Calls / max(SC_Total_Contracts_Interacted, 1)

Where:
- SC_Total_Last_Day_Calls = Smart contract calls in last 24 hours
- SC_Total_Contracts_Interacted = Total unique contracts used
- Range: [0, ∞]
- Higher values indicate more active smart contract usage
```

#### 6. Cross-Chain Ratio (5% weight - NEW)

```
SC_Cross_Chain_Ratio = SC_Unique_Chains / max(SC_Total_Contracts_Interacted, 1)

Where:
- SC_Unique_Chains = Number of unique blockchain networks used
- SC_Total_Contracts_Interacted = Total unique contracts used
- Range: [0, 1]
- Higher values indicate cross-chain sophistication
```

#### 7. Sophisticated User Score (10% weight - NEW)

```
Sophisticated_User_Score = (
    SC_Unique_Contract_Types × 0.3 +
    SC_Unique_Projects × 0.3 +
    Protocol_Diversity_Score × 0.4
)

Where:
- SC_Unique_Contract_Types = Diversity of contract types used
- SC_Unique_Projects = Number of unique DeFi projects
- Protocol_Diversity_Score = Traditional protocol diversity
- Range: [0, ∞]
- Measures DeFi expertise and sophistication
```

#### 8. Activity Consistency Score (5% weight - NEW)

```
Activity_Consistency_Score = {
    1.0 if (SC_Days_Since_Last_Activity < 30) AND (Activity_Frequency > 0.01)
    0.5 if moderate activity
    0.0 if (SC_Days_Since_Last_Activity > 365) OR (Activity_Frequency < 0.001)
}

Where:
- SC_Days_Since_Last_Activity = Days since last smart contract interaction
- Activity_Frequency = Traditional activity frequency metric
- Range: [0, 1]
- Measures temporal consistency of blockchain activity
```

## 🔗 Cross-Domain Enhanced Features

### Enhanced Feature Engineering (NEW)

#### Multi-Chain DeFi User Detection
```
Multi_Chain_DeFi_User = (SC_Unique_Chains > 1) AND (Total_Amount_Borrowed_USD > 0)

Where:
- Creates binary flag for users active in both DeFi lending and multiple chains
- Indicates sophisticated cross-chain DeFi usage
```

#### DeFi-Smart Contract Activity Ratio
```
DeFi_SC_Activity_Ratio = (Total_Borrows + Total_Repays + Total_Deposits) / (SC_Total_Contracts + 1)

Where:
- Measures balance between traditional DeFi and smart contract activity
- Higher values indicate DeFi-focused usage
```

#### Smart Contract Value Exposure Ratio
```
SC_DeFi_Value_Ratio = SC_Total_Market_Cap / (Total_Borrow_USD + Total_Deposit_USD + 1)

Where:
- SC_Total_Market_Cap = Total market cap of associated tokens
- Measures exposure to smart contract token values vs DeFi lending amounts
```

## 🎛️ Normalization Process

All component scores are normalized to [0, 1] range before applying weights:

```
Normalized_Score = (Raw_Score - Dataset_Min) / (Dataset_Max - Dataset_Min + ε)

Where:
- Raw_Score = original calculated metric
- Dataset_Min = minimum value in the dataset
- Dataset_Max = maximum value in the dataset
- ε = 1e-8 (prevents division by zero)
```

## ⚖️ Risk-Based Adjustments

### Model Ensemble Adjustment

```
Model_Adjustment = Risk_Adjustment_Factor[Ensemble_Risk_Prediction]

Where:
Risk_Adjustment_Factor = {
    0 (Low Risk): 1.0,      # No penalty
    1 (Medium Risk): 0.7,   # 30% reduction
    2 (High Risk): 0.3      # 70% reduction
}
```

### Ensemble Risk Prediction

```
Ensemble_Risk = argmax(Ensemble_Probabilities)

Where:
Ensemble_Probabilities = (RF_Probabilities + XGB_Probabilities) / 2

RF_Probabilities = Random_Forest.predict_proba(features)
XGB_Probabilities = XGBoost.predict_proba(features)
```

## 🚨 Enhanced Penalty & Bonus Factors

### Traditional Penalties

#### Liquidation Penalty
```
Liquidation_Penalty = {
    0.5 if wallet_address ∈ Liquidated_Wallets,
    1.0 otherwise
}
```

#### Anomaly Detection Penalty
```
Anomaly_Penalty = {
    0.5 if Isolation_Forest.predict(features) == -1,
    1.0 otherwise
}

Where:
- Isolation_Forest trained with contamination=0.1
- -1 indicates anomalous behavior (potential bot activity)
```

### NEW: Smart Contract Activity Adjustments

#### High Activity Bonus
```
SC_Activity_Bonus = {
    1.1 if SC_High_Activity_Flag == 1,
    1.0 otherwise
}

Where:
- SC_High_Activity_Flag = 1 if SC_Total_Last_Day_Calls > 100
- Provides 10% bonus for highly active smart contract users
```

#### Dormancy Penalty
```
Dormancy_Penalty = {
    0.8 if SC_Dormant_Wallet_Flag == 1,
    1.0 otherwise
}

Where:
- SC_Dormant_Wallet_Flag = 1 if SC_Days_Since_Last_Activity > 365
- Applies 20% penalty for dormant wallets
```

#### Cross-Chain Sophistication Bonus
```
Cross_Chain_Bonus = {
    1.05 if SC_Cross_Chain_Activity == 1 AND SC_Unique_Chains >= 3,
    1.0 otherwise
}

Where:
- Provides 5% bonus for sophisticated cross-chain users
- Requires activity on 3+ different blockchain networks
```

## 📈 Advanced Features

### Temporal Decay Factor (Optional Enhancement)

```
Temporal_Weight = exp(-λ × Days_Since_Transaction)

Where:
- λ = decay parameter (e.g., 0.01)
- Days_Since_Transaction = days since each transaction
- Applied to weight recent activity more heavily
```

### Cross-Chain Bonus (Optional Enhancement)

```
Cross_Chain_Bonus = 1 + (Number_of_Chains - 1) × 0.05

Where:
- Number_of_Chains = distinct blockchains used
- 5% bonus per additional chain
- Maximum bonus capped at 25%
```

## 🎯 Enhanced Score Interpretation

### Enhanced NFCS Score Ranges

```
Enhanced_Credit_Rating = {
    [900, 1000]: "Excellent" (AAA) - Sophisticated cross-chain DeFi users,
    [800, 899]:  "Very Good" (AA) - Active multi-chain users with good repayment,
    [700, 799]:  "Good" (A) - Consistent DeFi users with smart contract activity,
    [600, 699]:  "Fair" (BBB) - Regular DeFi users with moderate SC activity,
    [500, 599]:  "Poor" (BB) - Limited activity or poor repayment history,
    [400, 499]:  "Very Poor" (B) - Minimal activity or significant risks,
    [0, 399]:    "Unacceptable" (C) - High risk or dormant wallets
}
```

### Enhanced Risk Categories

```
Enhanced_Risk_Level = {
    Enhanced_NFCS ≥ 700: "Low Risk" - Eligible for undercollateralized loans,
    400 ≤ Enhanced_NFCS < 700: "Medium Risk" - Partial collateral required,
    Enhanced_NFCS < 400: "High Risk" - Full collateral or rejection
}
```

### Smart Contract Activity Classifications

```
SC_User_Type = {
    Sophisticated_User_Score > 10 AND SC_Cross_Chain_Activity == 1: "DeFi Expert",
    SC_Unique_Contract_Types > 5 AND SC_Activity_Intensity > 0.5: "Active User",
    SC_Total_Contracts_Interacted > 10: "Regular User",
    SC_Total_Contracts_Interacted > 0: "Casual User",
    SC_Total_Contracts_Interacted == 0: "Non-SC User"
}
```

### Cross-Chain Activity Levels

```
Cross_Chain_Level = {
    SC_Unique_Chains >= 4: "Multi-Chain Expert",
    SC_Unique_Chains == 3: "Cross-Chain User",
    SC_Unique_Chains == 2: "Dual-Chain User",
    SC_Unique_Chains == 1: "Single-Chain User",
    SC_Unique_Chains == 0: "No SC Activity"
}
```

## 🔄 Dynamic Updates

### Real-time Score Updates

```
NFCS_new = Recalculate_NFCS(
    Previous_Transactions + New_Transaction,
    Updated_Timestamps,
    Current_Model_Weights
)
```

### Incremental Updates (for efficiency)

```
NFCS_incremental = NFCS_old × (1 - α) + NFCS_transaction × α

Where:
- α = learning rate (e.g., 0.1)
- NFCS_transaction = score impact of new transaction
- Allows for efficient real-time updates
```

## 📊 Enhanced Example Calculation

### Sample Wallet Data (Enhanced)
```
# Traditional DeFi Data
Total_Borrowed: $10,000
Total_Repaid: $9,500
Total_Deposited: $12,000
Number_of_Borrows: 5
Number_of_Repays: 4
Unique_Pools: 3
Unique_Tokens: 2
Wallet_Age: 180 days
Liquidated: No
Anomalous: No

# NEW: Smart Contract Data
SC_Total_Contracts: 15
SC_Unique_Chains: 3 (Ethereum, BSC, Polygon)
SC_Last_Day_Calls: 25
SC_Unique_Contract_Types: 8
SC_Unique_Projects: 6
SC_Days_Since_Last_Activity: 2
SC_Total_Market_Cap: $50,000
SC_High_Activity_Flag: No
SC_Dormant_Flag: No
SC_Cross_Chain_Activity: Yes
```

### Enhanced Step-by-Step Calculation

#### Traditional DeFi Components
1. **Repayment Ratio**: 9,500 / 10,000 = 0.95
2. **Repayment Consistency**: 4 / 5 = 0.80
3. **Deposit/Borrow Ratio**: 12,000 / 10,000 = 1.20
4. **Protocol Diversity**: 3 × 2 = 6

#### NEW: Smart Contract Components
5. **SC Activity Intensity**: 25 / 15 = 1.67
6. **SC Cross-Chain Ratio**: 3 / 15 = 0.20
7. **Sophisticated User Score**: (8 × 0.3) + (6 × 0.3) + (6 × 0.4) = 6.6
8. **Activity Consistency Score**: 1.0 (recent activity + good frequency)

### Enhanced Normalization (assuming dataset ranges)

#### Traditional Components
```
Repayment_Ratio_Norm = (0.95 - 0) / (1.5 - 0) = 0.633
Repayment_Consistency_Norm = (0.80 - 0) / (1.2 - 0) = 0.667
Deposit_Borrow_Ratio_Norm = (1.20 - 0) / (5.0 - 0) = 0.240
Protocol_Diversity_Norm = (6 - 1) / (50 - 1) = 0.102
```

#### NEW: Smart Contract Components
```
SC_Activity_Intensity_Norm = (1.67 - 0) / (10.0 - 0) = 0.167
SC_Cross_Chain_Ratio_Norm = (0.20 - 0) / (1.0 - 0) = 0.200
Sophisticated_User_Score_Norm = (6.6 - 0) / (20.0 - 0) = 0.330
Activity_Consistency_Score_Norm = (1.0 - 0) / (1.0 - 0) = 1.000
```

### Enhanced Final NFCS Calculation

#### Traditional NFCS (Legacy)
```
Traditional_NFCS = (0.633×0.30 + 0.667×0.25 + 0.240×0.20 + 0.102×0.15) × 1000
Traditional_NFCS = (0.190 + 0.167 + 0.048 + 0.015) × 1000
Traditional_NFCS = 0.420 × 1000 = 420
```

#### Enhanced NFCS (Current)
```
Enhanced_NFCS = (
    # Traditional Components (70%)
    0.633×0.25 + 0.667×0.20 + 0.240×0.15 + 0.102×0.10 +
    # Smart Contract Components (30%)
    0.167×0.10 + 0.200×0.05 + 0.330×0.10 + 1.000×0.05
) × 1000

Enhanced_NFCS = (
    0.158 + 0.133 + 0.036 + 0.010 +
    0.017 + 0.010 + 0.033 + 0.050
) × 1000

Enhanced_NFCS = 0.447 × 1000 = 447
```

#### Apply Enhanced Penalties/Bonuses
```
Enhanced_NFCS_final = 447 × 1.0 × 1.0 × 1.05 × 1.0
Enhanced_NFCS_final = 447 × 1.05 = 469 (cross-chain bonus applied)
```

**Results Comparison**:
- **Traditional NFCS**: 420 (High Risk)
- **Enhanced NFCS**: 469 (Medium Risk)
- **Improvement**: +49 points (+11.7%) due to smart contract sophistication

## 🚀 Enhanced NFCS v2.0 Summary

### Key Improvements in Enhanced NFCS

#### 1. **Smart Contract Integration (30% weight)**
- **Activity Intensity**: Measures smart contract usage frequency
- **Cross-Chain Sophistication**: Rewards multi-blockchain activity
- **User Sophistication**: Identifies experienced DeFi users
- **Activity Consistency**: Temporal behavior analysis

#### 2. **Rebalanced Traditional Components (70% weight)**
- **Repayment Ratio**: 30% → 25% (still most important)
- **Repayment Consistency**: 25% → 20%
- **Deposit/Borrow Ratio**: 20% → 15%
- **Protocol Diversity**: 15% → 10%
- **Activity Frequency**: 10% → 0% (replaced by consistency score)

#### 3. **Enhanced Penalties & Bonuses**
- **High Activity Bonus**: +10% for active smart contract users
- **Cross-Chain Bonus**: +5% for sophisticated multi-chain users
- **Dormancy Penalty**: -20% for inactive wallets
- **Traditional Penalties**: Liquidation (-50%), Anomaly (-50%)

#### 4. **Improved Risk Assessment**
- **Better Granularity**: 21 additional smart contract features
- **Cross-Chain Analysis**: Multi-blockchain behavior patterns
- **Sophistication Detection**: Identifies DeFi experts vs casual users
- **Temporal Consistency**: Recent activity vs historical patterns

### Performance Impact

#### Score Distribution Changes
```
Traditional NFCS:
- Mean: 22.35/1000
- Median: 15.03/1000
- High Risk: 60.2%

Enhanced NFCS (Expected):
- Mean: ~35-45/1000 (improved due to SC activity)
- Better risk differentiation
- More accurate high-value user identification
```

#### Business Benefits
1. **Reduced False Negatives**: Sophisticated users no longer penalized
2. **Cross-Chain Recognition**: Multi-blockchain users properly scored
3. **Activity Rewards**: Active users get better scores
4. **Risk Granularity**: Better distinction between risk levels

### Implementation Status

#### ✅ Completed Features
- [x] Smart contract data processing (21 new features)
- [x] Enhanced NFCS calculation with SC components
- [x] Cross-chain activity analysis
- [x] Sophisticated user detection
- [x] Activity consistency scoring
- [x] Enhanced penalties and bonuses
- [x] Dual scoring (traditional + enhanced)

#### 🔄 Integration Points
- [x] ML Pipeline integration
- [x] Blockchain oracle updates
- [x] Cross-chain synchronization
- [x] Real-time scoring API
- [x] Smart contract deployment ready

This enhanced formula system provides a more comprehensive, accurate, and sophisticated credit scoring mechanism specifically designed for the modern multi-chain DeFi ecosystem.
