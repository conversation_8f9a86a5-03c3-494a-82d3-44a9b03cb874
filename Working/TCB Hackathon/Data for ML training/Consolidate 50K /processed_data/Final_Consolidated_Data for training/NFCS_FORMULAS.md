# NFCS (Non-Fungible Credit Score) - Mathematical Formulas

## 🎯 Overview

The NFCS (Non-Fungible Credit Score) is a comprehensive credit scoring system designed for DeFi lending protocols. It combines multiple behavioral and financial metrics to create a dynamic, blockchain-based credit score ranging from 0 to 1000.

## 📊 Core NFCS Formula

### Base NFCS Calculation

```
NFCS_base = (
    Repayment_Ratio_Normalized × 0.30 +
    Repayment_Consistency_Normalized × 0.25 +
    Deposit_Borrow_Ratio_Normalized × 0.20 +
    Protocol_Diversity_Normalized × 0.15 +
    Activity_Frequency_Normalized × 0.10
) × 1000
```

### Final NFCS with Adjustments

```
NFCS_final = NFCS_base × Liquidation_Penalty × Anomaly_Penalty × Model_Adjustment
```

Where:
- `Liquidation_Penalty = 0.5` if wallet has been liquidated, else `1.0`
- `Anomaly_Penalty = 0.5` if flagged as anomalous, else `1.0`
- `Model_Adjustment` = Risk-based adjustment from ensemble models

## 🔢 Component Formulas

### 1. Repayment Ratio (30% weight)

```
Repayment_Ratio = Total_Amount_Repaid_USD / Total_Amount_Borrowed_USD

Where:
- Total_Amount_Repaid_USD = Σ(all repayment transactions in USD)
- Total_Amount_Borrowed_USD = Σ(all borrow transactions in USD)
- Range: [0, ∞], typically [0, 1.5]
- Perfect repayment = 1.0
```

**Normalization:**
```
Repayment_Ratio_Normalized = (Repayment_Ratio - Min_Value) / (Max_Value - Min_Value + ε)

Where:
- Min_Value = minimum repayment ratio in dataset
- Max_Value = maximum repayment ratio in dataset  
- ε = 1e-8 (small constant to avoid division by zero)
```

### 2. Repayment Consistency (25% weight)

```
Repayment_Consistency = Total_Number_of_Repays / Total_Number_of_Borrows

Where:
- Total_Number_of_Repays = count of repayment transactions
- Total_Number_of_Borrows = count of borrow transactions
- Range: [0, ∞], typically [0, 1.2]
- Perfect consistency = 1.0
```

**Interpretation:**
- `< 1.0`: Some borrows not repaid
- `= 1.0`: Each borrow has corresponding repay
- `> 1.0`: Multiple repays per borrow (partial repayments)

### 3. Deposit to Borrow Ratio (20% weight)

```
Deposit_Borrow_Ratio = Total_Amount_Deposited_USD / Total_Amount_Borrowed_USD

Where:
- Total_Amount_Deposited_USD = Σ(all deposit transactions in USD)
- Total_Amount_Borrowed_USD = Σ(all borrow transactions in USD)
- Range: [0, ∞]
- Higher values indicate better collateralization
```

**Risk Interpretation:**
- `< 1.0`: Under-collateralized position
- `= 1.0`: Fully collateralized
- `> 1.0`: Over-collateralized (lower risk)

### 4. Protocol Diversity Score (15% weight)

```
Protocol_Diversity_Score = Unique_Lending_Pools × Unique_Tokens

Where:
- Unique_Lending_Pools = count of distinct lending pool addresses
- Unique_Tokens = count of distinct token addresses
- Range: [1, ∞]
- Higher values indicate diversified DeFi activity
```

**Enhanced Formula (optional):**
```
Protocol_Diversity_Enhanced = log(1 + Unique_Lending_Pools) × log(1 + Unique_Tokens) × Interaction_Factor

Where:
- Interaction_Factor = 1 + (Cross_Chain_Activity × 0.1)
- Cross_Chain_Activity = number of different blockchains used
```

### 5. Activity Frequency (10% weight)

```
Activity_Frequency = Borrow_Frequency / (Wallet_Age_Days + 1)

Where:
- Borrow_Frequency = Total_Number_of_Borrows
- Wallet_Age_Days = (Last_Borrow_Date - First_Borrow_Date).days
- +1 prevents division by zero for same-day activity
- Range: [0, ∞]
```

**Alternative Formula:**
```
Activity_Frequency_Weighted = (
    Borrow_Frequency × 0.4 +
    Deposit_Frequency × 0.3 +
    Repay_Frequency × 0.3
) / (Wallet_Age_Days + 1)
```

## 🎛️ Normalization Process

All component scores are normalized to [0, 1] range before applying weights:

```
Normalized_Score = (Raw_Score - Dataset_Min) / (Dataset_Max - Dataset_Min + ε)

Where:
- Raw_Score = original calculated metric
- Dataset_Min = minimum value in the dataset
- Dataset_Max = maximum value in the dataset
- ε = 1e-8 (prevents division by zero)
```

## ⚖️ Risk-Based Adjustments

### Model Ensemble Adjustment

```
Model_Adjustment = Risk_Adjustment_Factor[Ensemble_Risk_Prediction]

Where:
Risk_Adjustment_Factor = {
    0 (Low Risk): 1.0,      # No penalty
    1 (Medium Risk): 0.7,   # 30% reduction
    2 (High Risk): 0.3      # 70% reduction
}
```

### Ensemble Risk Prediction

```
Ensemble_Risk = argmax(Ensemble_Probabilities)

Where:
Ensemble_Probabilities = (RF_Probabilities + XGB_Probabilities) / 2

RF_Probabilities = Random_Forest.predict_proba(features)
XGB_Probabilities = XGBoost.predict_proba(features)
```

## 🚨 Penalty Factors

### Liquidation Penalty

```
Liquidation_Penalty = {
    0.5 if wallet_address ∈ Liquidated_Wallets,
    1.0 otherwise
}
```

### Anomaly Detection Penalty

```
Anomaly_Penalty = {
    0.5 if Isolation_Forest.predict(features) == -1,
    1.0 otherwise
}

Where:
- Isolation_Forest trained with contamination=0.1
- -1 indicates anomalous behavior (potential bot activity)
```

## 📈 Advanced Features

### Temporal Decay Factor (Optional Enhancement)

```
Temporal_Weight = exp(-λ × Days_Since_Transaction)

Where:
- λ = decay parameter (e.g., 0.01)
- Days_Since_Transaction = days since each transaction
- Applied to weight recent activity more heavily
```

### Cross-Chain Bonus (Optional Enhancement)

```
Cross_Chain_Bonus = 1 + (Number_of_Chains - 1) × 0.05

Where:
- Number_of_Chains = distinct blockchains used
- 5% bonus per additional chain
- Maximum bonus capped at 25%
```

## 🎯 Score Interpretation

### NFCS Score Ranges

```
Credit Rating = {
    [900, 1000]: "Excellent" (AAA),
    [800, 899]:  "Very Good" (AA),
    [700, 799]:  "Good" (A),
    [600, 699]:  "Fair" (BBB),
    [500, 599]:  "Poor" (BB),
    [400, 499]:  "Very Poor" (B),
    [0, 399]:    "Unacceptable" (C)
}
```

### Risk Categories

```
Risk_Level = {
    NFCS ≥ 700: "Low Risk",
    400 ≤ NFCS < 700: "Medium Risk",
    NFCS < 400: "High Risk"
}
```

## 🔄 Dynamic Updates

### Real-time Score Updates

```
NFCS_new = Recalculate_NFCS(
    Previous_Transactions + New_Transaction,
    Updated_Timestamps,
    Current_Model_Weights
)
```

### Incremental Updates (for efficiency)

```
NFCS_incremental = NFCS_old × (1 - α) + NFCS_transaction × α

Where:
- α = learning rate (e.g., 0.1)
- NFCS_transaction = score impact of new transaction
- Allows for efficient real-time updates
```

## 📊 Example Calculation

### Sample Wallet Data
```
Total_Borrowed: $10,000
Total_Repaid: $9,500
Total_Deposited: $12,000
Number_of_Borrows: 5
Number_of_Repays: 4
Unique_Pools: 3
Unique_Tokens: 2
Wallet_Age: 180 days
Liquidated: No
Anomalous: No
```

### Step-by-Step Calculation

1. **Repayment Ratio**: 9,500 / 10,000 = 0.95
2. **Repayment Consistency**: 4 / 5 = 0.80
3. **Deposit/Borrow Ratio**: 12,000 / 10,000 = 1.20
4. **Protocol Diversity**: 3 × 2 = 6
5. **Activity Frequency**: 5 / (180 + 1) = 0.0276

### Normalization (assuming dataset ranges)
```
Repayment_Ratio_Norm = (0.95 - 0) / (1.5 - 0) = 0.633
Repayment_Consistency_Norm = (0.80 - 0) / (1.2 - 0) = 0.667
Deposit_Borrow_Ratio_Norm = (1.20 - 0) / (5.0 - 0) = 0.240
Protocol_Diversity_Norm = (6 - 1) / (50 - 1) = 0.102
Activity_Frequency_Norm = (0.0276 - 0) / (0.5 - 0) = 0.055
```

### Final NFCS Calculation
```
NFCS_base = (0.633×0.30 + 0.667×0.25 + 0.240×0.20 + 0.102×0.15 + 0.055×0.10) × 1000
NFCS_base = (0.190 + 0.167 + 0.048 + 0.015 + 0.006) × 1000
NFCS_base = 0.426 × 1000 = 426

NFCS_final = 426 × 1.0 × 1.0 × 0.7 = 298 (assuming medium risk prediction)
```

**Result**: NFCS Score = 298 (High Risk category)

This comprehensive formula system provides a robust, interpretable, and dynamic credit scoring mechanism specifically designed for the DeFi ecosystem.
