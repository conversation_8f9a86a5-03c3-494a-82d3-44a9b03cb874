#!/usr/bin/env python3
"""
DeFi Credit Scoring Web UI - Simplified Version
Interactive dashboard for credit scoring and data visualization
"""

import streamlit as st

# Page configuration - MUST be first Streamlit command
st.set_page_config(
    page_title="DeFi Credit Scoring Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our pipeline (simplified imports)
try:
    from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
    BASIC_PIPELINE_AVAILABLE = True
except ImportError as e:
    st.error(f"Could not import basic pipeline: {e}")
    BASIC_PIPELINE_AVAILABLE = False

try:
    from enhanced_defi_pipeline import EnhancedDeFiPipeline
    from smart_contract_processor import SmartContractProcessor
    ENHANCED_PIPELINE_AVAILABLE = True
except ImportError as e:
    st.warning(f"Enhanced pipeline not available: {e}")
    ENHANCED_PIPELINE_AVAILABLE = False

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #1f77b4;
    }
    .risk-low {
        color: #28a745;
        font-weight: bold;
    }
    .risk-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .risk-high {
        color: #dc3545;
        font-weight: bold;
    }
    .enhanced-score {
        background: linear-gradient(90deg, #1f77b4, #17a2b8);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_basic_pipeline():
    """Load the basic trained pipeline"""
    if not BASIC_PIPELINE_AVAILABLE:
        return None
    
    try:
        pipeline = DeFiCreditScoringPipeline(".")
        pipeline.load_data()
        pipeline.preprocess_data()
        pipeline.engineer_features()
        pipeline.create_target_variable()
        pipeline.train_models()
        return pipeline
    except Exception as e:
        st.error(f"Error loading basic pipeline: {e}")
        return None

@st.cache_data
def load_enhanced_pipeline():
    """Load the enhanced pipeline with smart contract features"""
    if not ENHANCED_PIPELINE_AVAILABLE:
        return None
    
    try:
        enhanced_pipeline = EnhancedDeFiPipeline(".")
        enhanced_pipeline.run_enhanced_pipeline()
        return enhanced_pipeline
    except Exception as e:
        st.error(f"Error loading enhanced pipeline: {e}")
        return None

def main():
    # Header
    st.markdown('<h1 class="main-header">🏦 DeFi Credit Scoring Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Pipeline selection
    pipeline_type = st.sidebar.selectbox(
        "Select Pipeline Type",
        ["Basic Pipeline", "Enhanced Pipeline (with Smart Contracts)"]
    )
    
    # Load appropriate pipeline
    if pipeline_type == "Basic Pipeline":
        with st.spinner("Loading basic ML pipeline..."):
            pipeline = load_basic_pipeline()
        enhanced_pipeline = None
    else:
        with st.spinner("Loading enhanced ML pipeline..."):
            enhanced_pipeline = load_enhanced_pipeline()
        pipeline = enhanced_pipeline.defi_pipeline if enhanced_pipeline else None
    
    if pipeline is None:
        st.error("Failed to load the ML pipeline. Please check your data files.")
        return
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Wallet Scoring", "Data Visualization", "Model Performance", "Smart Contract Analysis"]
    )
    
    if page == "Overview":
        show_overview(pipeline, enhanced_pipeline)
    elif page == "Wallet Scoring":
        show_wallet_scoring(pipeline, enhanced_pipeline)
    elif page == "Data Visualization":
        show_data_visualization(pipeline, enhanced_pipeline)
    elif page == "Model Performance":
        show_model_performance(pipeline, enhanced_pipeline)
    elif page == "Smart Contract Analysis":
        show_smart_contract_analysis(enhanced_pipeline)

def show_overview(pipeline, enhanced_pipeline=None):
    """Show overview dashboard"""
    st.header("📊 Dataset Overview")
    
    # Determine which dataset to use
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        st.success("✅ Using Enhanced Pipeline with Smart Contract Features")
    else:
        features_df = pipeline.features_df
        st.info("ℹ️ Using Basic Pipeline")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Wallets", f"{len(features_df):,}")
    
    with col2:
        if enhanced_pipeline and 'enhanced_nfcs_score' in features_df.columns:
            avg_nfcs = features_df['enhanced_nfcs_score'].mean()
            st.metric("Average Enhanced NFCS", f"{avg_nfcs:.1f}")
        else:
            avg_nfcs = features_df['nfcs_score'].mean()
            st.metric("Average NFCS Score", f"{avg_nfcs:.1f}")
    
    with col3:
        liquidation_rate = features_df['has_been_liquidated'].mean() * 100
        st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
    
    with col4:
        high_risk_pct = (features_df['credit_risk'] == 2).mean() * 100
        st.metric("High Risk Wallets", f"{high_risk_pct:.1f}%")
    
    # Enhanced features info
    if enhanced_pipeline:
        st.subheader("🔗 Smart Contract Integration")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if 'sc_total_contracts_interacted' in features_df.columns:
                avg_contracts = features_df['sc_total_contracts_interacted'].mean()
                st.metric("Avg Contracts per Wallet", f"{avg_contracts:.1f}")
        
        with col2:
            if 'sc_unique_chains' in features_df.columns:
                cross_chain_users = (features_df['sc_unique_chains'] > 1).sum()
                st.metric("Cross-Chain Users", f"{cross_chain_users:,}")
        
        with col3:
            if 'sc_cross_chain_activity' in features_df.columns:
                sophisticated_users = features_df['sc_cross_chain_activity'].sum()
                st.metric("Multi-Chain Active", f"{sophisticated_users:,}")
    
    # Risk distribution
    st.subheader("Risk Distribution")
    risk_counts = features_df['credit_risk'].value_counts().sort_index()
    risk_labels = ['Low Risk', 'Medium Risk', 'High Risk']
    
    fig = px.pie(
        values=risk_counts.values,
        names=risk_labels,
        title="Credit Risk Distribution",
        color_discrete_sequence=['#28a745', '#ffc107', '#dc3545']
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # NFCS Score comparison
    if enhanced_pipeline and 'enhanced_nfcs_score' in features_df.columns:
        st.subheader("📈 NFCS Score Comparison")
        
        col1, col2 = st.columns(2)
        
        with col1:
            fig = px.histogram(
                features_df,
                x='nfcs_score',
                nbins=50,
                title="Traditional NFCS Distribution",
                labels={'nfcs_score': 'Traditional NFCS Score', 'count': 'Number of Wallets'}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            fig = px.histogram(
                features_df,
                x='enhanced_nfcs_score',
                nbins=50,
                title="Enhanced NFCS Distribution",
                labels={'enhanced_nfcs_score': 'Enhanced NFCS Score', 'count': 'Number of Wallets'}
            )
            st.plotly_chart(fig, use_container_width=True)
    else:
        st.subheader("NFCS Score Distribution")
        fig = px.histogram(
            features_df,
            x='nfcs_score',
            nbins=50,
            title="Distribution of NFCS Scores",
            labels={'nfcs_score': 'NFCS Score', 'count': 'Number of Wallets'}
        )
        fig.add_vline(x=avg_nfcs, line_dash="dash", line_color="red", 
                      annotation_text=f"Mean: {avg_nfcs:.1f}")
        st.plotly_chart(fig, use_container_width=True)

def show_wallet_scoring(pipeline, enhanced_pipeline=None):
    """Show individual wallet scoring interface"""
    st.header("🔍 Individual Wallet Scoring")
    
    # Determine which dataset to use
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        use_enhanced = True
    else:
        features_df = pipeline.features_df
        use_enhanced = False
    
    # Wallet selection
    wallet_options = features_df['walletAddress'].tolist()
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_wallet = st.selectbox(
            "Select a wallet address:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
    
    with col2:
        if st.button("Calculate NFCS Score", type="primary"):
            with st.spinner("Calculating score..."):
                if use_enhanced:
                    score_info = enhanced_pipeline.calculate_enhanced_nfcs_score(selected_wallet)
                else:
                    score_info = pipeline.calculate_nfcs_score(selected_wallet)
                st.session_state.score_info = score_info
                st.session_state.use_enhanced = use_enhanced
    
    # Display results
    if hasattr(st.session_state, 'score_info') and 'error' not in st.session_state.score_info:
        score_info = st.session_state.score_info
        use_enhanced = st.session_state.get('use_enhanced', False)
        
        if use_enhanced:
            show_enhanced_scoring_results(score_info)
        else:
            show_basic_scoring_results(score_info)

def show_enhanced_scoring_results(score_info):
    """Show enhanced scoring results with smart contract features"""
    st.markdown('<div class="enhanced-score">', unsafe_allow_html=True)
    st.markdown("### 🚀 Enhanced NFCS Scoring Results")
    st.markdown('</div>', unsafe_allow_html=True)
    
    # Score comparison
    col1, col2, col3 = st.columns(3)
    
    with col1:
        enhanced_score = score_info['enhanced_nfcs_score']
        traditional_score = score_info['traditional_nfcs_score']
        improvement = enhanced_score - traditional_score
        
        st.metric(
            "Enhanced NFCS Score", 
            f"{enhanced_score:.1f}/1000",
            delta=f"+{improvement:.1f} vs Traditional"
        )
    
    with col2:
        st.metric("Traditional NFCS Score", f"{traditional_score:.1f}/1000")
    
    with col3:
        risk_level = score_info['risk_level']
        risk_class = f"risk-{risk_level.lower()}"
        st.markdown(f'<p class="{risk_class}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
    
    # Smart Contract Insights
    st.subheader("🔗 Smart Contract Insights")
    
    sc_activity = score_info['smart_contract_insights']['smart_contract_activity']
    defi_integration = score_info['smart_contract_insights']['defi_integration']
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Smart Contract Activity:**")
        st.write(f"- Total Contracts: {sc_activity['total_contracts']}")
        st.write(f"- Unique Chains: {sc_activity['unique_chains']}")
        st.write(f"- Cross-Chain User: {'✅' if sc_activity['cross_chain_user'] else '❌'}")
        st.write(f"- Contract Creator: {'✅' if sc_activity['contract_creator'] else '❌'}")
        st.write(f"- Activity Intensity: {sc_activity['activity_intensity']:.3f}")
    
    with col2:
        st.write("**DeFi Integration:**")
        st.write(f"- Sophisticated Score: {defi_integration['sophisticated_user_score']:.2f}")
        st.write(f"- Multi-Chain DeFi: {'✅' if defi_integration['multi_chain_defi_user'] else '❌'}")
        st.write(f"- Activity Consistency: {defi_integration['activity_consistency']:.3f}")
    
    # Traditional metrics
    st.subheader("📊 Traditional DeFi Metrics")
    show_traditional_metrics(score_info['key_metrics'])

def show_basic_scoring_results(score_info):
    """Show basic scoring results"""
    # Main score display
    col1, col2, col3 = st.columns(3)
    
    with col1:
        nfcs_score = score_info['nfcs_score']
        st.metric("NFCS Score", f"{nfcs_score:.1f}/1000")
        
        # Score gauge
        fig = go.Figure(go.Indicator(
            mode = "gauge+number",
            value = nfcs_score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "NFCS Score"},
            gauge = {
                'axis': {'range': [None, 1000]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 300], 'color': "lightgray"},
                    {'range': [300, 700], 'color': "gray"},
                    {'range': [700, 1000], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 500
                }
            }
        ))
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        risk_level = score_info['risk_level']
        risk_class = f"risk-{risk_level.lower()}"
        st.markdown(f'<p class="{risk_class}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
        
        # Risk probabilities
        probs = score_info['risk_probabilities']
        prob_df = pd.DataFrame({
            'Risk Level': ['Low', 'Medium', 'High'],
            'Probability': [probs['low'], probs['medium'], probs['high']]
        })
        
        fig = px.bar(prob_df, x='Risk Level', y='Probability', 
                    title="Risk Probabilities",
                    color='Risk Level',
                    color_discrete_map={'Low': '#28a745', 'Medium': '#ffc107', 'High': '#dc3545'})
        st.plotly_chart(fig, use_container_width=True)
    
    with col3:
        show_traditional_metrics(score_info['key_metrics'])

def show_traditional_metrics(metrics):
    """Show traditional DeFi metrics"""
    st.subheader("Key Metrics")
    
    st.metric("Repayment Ratio", f"{metrics['repayment_ratio']:.3f}")
    st.metric("Repayment Consistency", f"{metrics['repayment_consistency']:.3f}")
    st.metric("Deposit/Borrow Ratio", f"{metrics['deposit_to_borrow_ratio']:.3f}")
    st.metric("Protocol Diversity", f"{metrics['protocol_diversity_score']:.1f}")
    
    if metrics['has_been_liquidated']:
        st.error("⚠️ Wallet has been liquidated")
    else:
        st.success("✅ No liquidation history")

def show_data_visualization(pipeline, enhanced_pipeline=None):
    """Show data visualization dashboard"""
    st.header("📈 Data Visualization")
    
    # Determine which dataset to use
    if enhanced_pipeline and hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        st.success("✅ Showing Enhanced Dataset with Smart Contract Features")
    else:
        features_df = pipeline.features_df
        st.info("ℹ️ Showing Basic Dataset")
    
    # Feature correlation heatmap
    st.subheader("Feature Correlations")
    numeric_features = features_df.select_dtypes(include=[np.number]).columns
    corr_matrix = features_df[numeric_features].corr()
    
    fig = px.imshow(corr_matrix, 
                    title="Feature Correlation Matrix",
                    color_continuous_scale='RdBu_r',
                    aspect="auto")
    st.plotly_chart(fig, use_container_width=True)
    
    # Feature distributions
    st.subheader("Feature Distributions")
    
    # Available features for plotting
    available_features = ['nfcs_score', 'repayment_ratio', 'totalAmountOfBorrowInUSD', 
                         'totalAmountOfDepositInUSD', 'protocol_diversity_score']
    
    # Add enhanced features if available
    if enhanced_pipeline:
        enhanced_features = ['enhanced_nfcs_score', 'sc_activity_intensity', 
                           'sc_cross_chain_ratio', 'sophisticated_user_score']
        available_features.extend([f for f in enhanced_features if f in features_df.columns])
    
    feature_to_plot = st.selectbox(
        "Select feature to visualize:",
        options=[f for f in available_features if f in features_df.columns]
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Histogram
        fig = px.histogram(features_df, x=feature_to_plot, 
                          title=f"Distribution of {feature_to_plot}")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Box plot by risk level
        fig = px.box(features_df, x='credit_risk', y=feature_to_plot,
                    title=f"{feature_to_plot} by Risk Level")
        st.plotly_chart(fig, use_container_width=True)

def show_model_performance(pipeline, enhanced_pipeline=None):
    """Show model performance metrics"""
    st.header("🎯 Model Performance")
    
    if enhanced_pipeline:
        st.subheader("Enhanced Model Performance")
        st.success("✅ Enhanced models with smart contract features trained")
        
        # You would add enhanced model evaluation here
        st.info("Enhanced model performance metrics would be displayed here")
    
    # Basic model performance
    st.subheader("Basic Model Performance")
    
    if hasattr(pipeline, 'evaluation_results'):
        evaluation_results = pipeline.evaluation_results
        
        # Model accuracy comparison
        accuracies = {}
        for model_name, results in evaluation_results.items():
            if 'accuracy' in results:
                accuracies[model_name] = results['accuracy']
        
        if accuracies:
            acc_df = pd.DataFrame(list(accuracies.items()), columns=['Model', 'Accuracy'])
            fig = px.bar(acc_df, x='Model', y='Accuracy', title="Model Accuracy Comparison")
            st.plotly_chart(fig, use_container_width=True)
    
    # Feature importance
    if hasattr(pipeline, 'feature_importance') and pipeline.feature_importance:
        st.subheader("Feature Importance")
        
        model_choice = st.selectbox("Select model:", options=list(pipeline.feature_importance.keys()))
        
        importance_data = pipeline.feature_importance[model_choice]
        importance_df = pd.DataFrame(list(importance_data.items()), 
                                   columns=['Feature', 'Importance']).sort_values('Importance', ascending=True)
        
        # Top 15 features
        top_features = importance_df.tail(15)
        fig = px.bar(top_features, x='Importance', y='Feature', orientation='h',
                    title=f"Top 15 Features - {model_choice.title()}")
        st.plotly_chart(fig, use_container_width=True)

def show_smart_contract_analysis(enhanced_pipeline):
    """Show smart contract specific analysis"""
    st.header("🔗 Smart Contract Analysis")
    
    if not enhanced_pipeline:
        st.warning("Enhanced pipeline with smart contract features is not available.")
        st.info("To enable smart contract analysis, ensure all dependencies are installed and the enhanced pipeline is loaded.")
        return
    
    if not hasattr(enhanced_pipeline, 'sc_processor'):
        st.warning("Smart contract processor not available in enhanced pipeline.")
        return
    
    st.success("✅ Smart Contract Analysis Available")
    
    # Smart contract statistics
    if hasattr(enhanced_pipeline, 'enhanced_features_df'):
        features_df = enhanced_pipeline.enhanced_features_df
        
        st.subheader("📊 Smart Contract Statistics")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if 'sc_total_contracts_interacted' in features_df.columns:
                total_contracts = features_df['sc_total_contracts_interacted'].sum()
                st.metric("Total SC Interactions", f"{total_contracts:,}")
        
        with col2:
            if 'sc_unique_chains' in features_df.columns:
                max_chains = features_df['sc_unique_chains'].max()
                st.metric("Max Chains Used", f"{max_chains}")
        
        with col3:
            if 'sc_cross_chain_activity' in features_df.columns:
                cross_chain_users = features_df['sc_cross_chain_activity'].sum()
                st.metric("Cross-Chain Users", f"{cross_chain_users:,}")
        
        with col4:
            if 'sc_high_activity_flag' in features_df.columns:
                high_activity = features_df['sc_high_activity_flag'].sum()
                st.metric("High Activity Users", f"{high_activity:,}")
        
        # Smart contract activity distribution
        if 'sc_activity_intensity' in features_df.columns:
            st.subheader("Smart Contract Activity Distribution")
            
            fig = px.histogram(
                features_df,
                x='sc_activity_intensity',
                nbins=50,
                title="Smart Contract Activity Intensity Distribution"
            )
            st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    main()
