#!/usr/bin/env python3
"""
DeFi Credit Scoring Web UI - Fixed Version
Handles autoencoder caching issues
"""

import streamlit as st

# Page configuration - MUST be first
st.set_page_config(
    page_title="DeFi Credit Scoring Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import warnings
warnings.filterwarnings('ignore')

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .risk-low { color: #28a745; font-weight: bold; }
    .risk-medium { color: #ffc107; font-weight: bold; }
    .risk-high { color: #dc3545; font-weight: bold; }
    .enhanced-score {
        background: linear-gradient(90deg, #1f77b4, #17a2b8);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Global pipeline variable to avoid caching issues
if 'pipeline' not in st.session_state:
    st.session_state.pipeline = None
if 'pipeline_loaded' not in st.session_state:
    st.session_state.pipeline_loaded = False

def load_pipeline_without_cache():
    """Load pipeline without caching to avoid autoencoder issues"""
    if st.session_state.pipeline_loaded and st.session_state.pipeline is not None:
        return st.session_state.pipeline, None
    
    try:
        from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
        
        with st.spinner("Loading DeFi Credit Scoring Pipeline..."):
            pipeline = DeFiCreditScoringPipeline(".")
            pipeline.load_data()
            pipeline.preprocess_data()
            pipeline.engineer_features()
            pipeline.create_target_variable()
            pipeline.train_models()
            
            # Store in session state
            st.session_state.pipeline = pipeline
            st.session_state.pipeline_loaded = True
            
            return pipeline, None
    except Exception as e:
        st.error(f"Error loading pipeline: {e}")
        return None, str(e)

def main():
    # Header
    st.markdown('<h1 class="main-header">🏦 DeFi Credit Scoring Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load pipeline
    pipeline, error = load_pipeline_without_cache()
    
    if pipeline is None:
        st.error(f"Failed to load pipeline: {error}")
        st.info("Please ensure all data files are present and dependencies are installed.")
        return
    
    # Success message
    if st.session_state.pipeline_loaded:
        st.success("✅ Pipeline loaded successfully with advanced algorithms!")
        
        # Show pipeline stats
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Wallets", f"{len(pipeline.features_df):,}")
        with col2:
            avg_nfcs = pipeline.features_df['nfcs_score'].mean()
            st.metric("Average NFCS", f"{avg_nfcs:.1f}")
        with col3:
            if hasattr(pipeline, 'anomaly_detectors'):
                st.metric("Anomaly Detectors", len(pipeline.anomaly_detectors))
        with col4:
            if hasattr(pipeline, 'clustering_models'):
                st.metric("Clustering Models", len(pipeline.clustering_models))
    
    # Navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Wallet Scoring", "Advanced Algorithms", "New User Clustering"]
    )
    
    if page == "Overview":
        show_overview(pipeline)
    elif page == "Wallet Scoring":
        show_wallet_scoring(pipeline)
    elif page == "Advanced Algorithms":
        show_advanced_algorithms(pipeline)
    elif page == "New User Clustering":
        show_new_user_clustering(pipeline)

def show_overview(pipeline):
    """Show overview dashboard"""
    st.header("📊 Dataset Overview")
    
    features_df = pipeline.features_df
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Wallets", f"{len(features_df):,}")
    
    with col2:
        avg_nfcs = features_df['nfcs_score'].mean()
        st.metric("Average NFCS Score", f"{avg_nfcs:.1f}")
    
    with col3:
        liquidation_rate = features_df['has_been_liquidated'].mean() * 100
        st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
    
    with col4:
        high_risk_pct = (features_df['credit_risk'] == 2).mean() * 100
        st.metric("High Risk Wallets", f"{high_risk_pct:.1f}%")
    
    # Advanced algorithms status
    st.subheader("🔬 Advanced Algorithms Status")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Anomaly Detection:**")
        if hasattr(pipeline, 'anomaly_detectors'):
            for detector in pipeline.anomaly_detectors.keys():
                st.write(f"✅ {detector.replace('_', ' ').title()}")
            
            if 'is_anomaly' in features_df.columns:
                anomaly_count = features_df['is_anomaly'].sum()
                st.metric("Total Anomalies", f"{anomaly_count:,}")
        else:
            st.write("❌ Advanced anomaly detection not available")
    
    with col2:
        st.write("**Clustering:**")
        if hasattr(pipeline, 'clustering_models'):
            for cluster_model in pipeline.clustering_models.keys():
                st.write(f"✅ {cluster_model.replace('_', ' ').title()}")
            
            if 'dbscan_cluster' in features_df.columns:
                n_clusters = features_df['dbscan_cluster'].nunique() - 1  # Exclude noise (-1)
                noise_points = (features_df['dbscan_cluster'] == -1).sum()
                st.metric("DBSCAN Clusters", f"{n_clusters}")
                st.metric("Noise Points", f"{noise_points:,}")
        else:
            st.write("❌ Advanced clustering not available")
    
    # Risk distribution
    st.subheader("Risk Distribution")
    risk_counts = features_df['credit_risk'].value_counts().sort_index()
    risk_labels = ['Low Risk', 'Medium Risk', 'High Risk']
    
    fig = px.pie(
        values=risk_counts.values,
        names=risk_labels,
        title="Credit Risk Distribution",
        color_discrete_sequence=['#28a745', '#ffc107', '#dc3545']
    )
    st.plotly_chart(fig, use_container_width=True)

def show_wallet_scoring(pipeline):
    """Show wallet scoring interface"""
    st.header("🔍 Individual Wallet Scoring")
    
    features_df = pipeline.features_df
    
    # Wallet selection
    wallet_options = features_df['walletAddress'].tolist()
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_wallet = st.selectbox(
            "Select a wallet address:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
    
    with col2:
        if st.button("Calculate NFCS Score", type="primary"):
            with st.spinner("Calculating score..."):
                try:
                    score_info = pipeline.calculate_nfcs_score(selected_wallet)
                    st.session_state.score_info = score_info
                except Exception as e:
                    st.error(f"Error calculating score: {e}")
    
    # Display results
    if hasattr(st.session_state, 'score_info') and 'error' not in st.session_state.score_info:
        score_info = st.session_state.score_info
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            nfcs_score = score_info['nfcs_score']
            st.metric("NFCS Score", f"{nfcs_score:.1f}/1000")
            
            # Score gauge
            fig = go.Figure(go.Indicator(
                mode = "gauge+number",
                value = nfcs_score,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "NFCS Score"},
                gauge = {
                    'axis': {'range': [None, 1000]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 300], 'color': "lightgray"},
                        {'range': [300, 700], 'color': "gray"},
                        {'range': [700, 1000], 'color': "lightgreen"}
                    ]
                }
            ))
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            risk_level = score_info['risk_level']
            st.markdown(f'<p class="risk-{risk_level.lower()}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
            
            # Show advanced anomaly detection if available
            if 'advanced_anomaly_detection' in score_info:
                st.subheader("🚨 Anomaly Analysis")
                anomaly_info = score_info['advanced_anomaly_detection']
                
                anomaly_count = 0
                total_methods = 0
                
                for method, result in anomaly_info.items():
                    if isinstance(result, dict) and 'is_anomaly' in result:
                        total_methods += 1
                        if result['is_anomaly']:
                            anomaly_count += 1
                            st.write(f"🚨 {method.replace('_', ' ').title()}")
                        else:
                            st.write(f"✅ {method.replace('_', ' ').title()}")
                
                if total_methods > 0:
                    st.metric("Anomaly Score", f"{anomaly_count}/{total_methods}")
        
        with col3:
            metrics = score_info['key_metrics']
            st.subheader("Key Metrics")
            st.metric("Repayment Ratio", f"{metrics['repayment_ratio']:.3f}")
            st.metric("Repayment Consistency", f"{metrics['repayment_consistency']:.3f}")
            
            if metrics['has_been_liquidated']:
                st.error("⚠️ Wallet has been liquidated")
            else:
                st.success("✅ No liquidation history")

def show_advanced_algorithms(pipeline):
    """Show advanced algorithms analysis"""
    st.header("🔬 Advanced Algorithms Analysis")
    
    # Check if advanced algorithms are available
    has_advanced = (hasattr(pipeline, 'anomaly_detectors') and 
                   hasattr(pipeline, 'clustering_models'))
    
    if not has_advanced:
        st.warning("Advanced algorithms not yet trained. Please restart the application.")
        return
    
    st.success("✅ Advanced algorithms are available!")
    
    # Anomaly Detection Section
    st.subheader("🚨 Advanced Anomaly Detection")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Available Methods:**")
        methods = list(pipeline.anomaly_detectors.keys())
        if hasattr(pipeline, 'autoencoder') and pipeline.autoencoder:
            methods.append("autoencoder")
        
        for method in methods:
            st.write(f"✅ {method.replace('_', ' ').title()}")
    
    with col2:
        # Anomaly statistics
        if 'is_anomaly' in pipeline.features_df.columns:
            total_anomalies = pipeline.features_df['is_anomaly'].sum()
            anomaly_rate = (total_anomalies / len(pipeline.features_df)) * 100
            
            st.metric("Total Anomalies", f"{total_anomalies:,}")
            st.metric("Anomaly Rate", f"{anomaly_rate:.2f}%")
    
    # Clustering Section
    st.subheader("🔗 Advanced Clustering Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Available Methods:**")
        for method in pipeline.clustering_models.keys():
            st.write(f"✅ {method.replace('_', ' ').title()}")
    
    with col2:
        # Clustering statistics
        if 'enhanced_kmeans_cluster' in pipeline.features_df.columns:
            n_clusters = pipeline.features_df['enhanced_kmeans_cluster'].nunique()
            st.metric("K-means Clusters", n_clusters)
        
        if 'dbscan_cluster' in pipeline.features_df.columns:
            n_dbscan = pipeline.features_df['dbscan_cluster'].nunique()
            noise_points = (pipeline.features_df['dbscan_cluster'] == -1).sum()
            st.metric("DBSCAN Clusters", f"{n_dbscan-1}")
            st.metric("Noise Points", f"{noise_points:,}")

def show_new_user_clustering(pipeline):
    """Show clustering analysis for new users"""
    st.header("👥 New User Clustering Analysis")
    
    # Find users without loan history
    no_loan_users = pipeline.features_df[
        pipeline.features_df['totalAmountOfBorrowInUSD'] == 0
    ]
    
    st.subheader("📊 New User Statistics")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total New Users", f"{len(no_loan_users):,}")
    
    with col2:
        total_users = len(pipeline.features_df)
        new_user_pct = (len(no_loan_users) / total_users) * 100
        st.metric("Percentage of New Users", f"{new_user_pct:.1f}%")
    
    with col3:
        if 'dbscan_initial_score' in no_loan_users.columns:
            avg_initial_score = no_loan_users['dbscan_initial_score'].mean()
            st.metric("Avg Initial Score", f"{avg_initial_score:.1f}")
    
    # Test clustering for new users
    st.subheader("🔍 Test New User Clustering")
    
    if len(no_loan_users) > 0:
        new_user_options = no_loan_users['walletAddress'].head(10).tolist()  # Limit for performance
        selected_new_user = st.selectbox(
            "Select new user for clustering analysis:",
            options=new_user_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
        
        if st.button("Analyze Clustering", type="primary"):
            try:
                cluster_result = pipeline.get_cluster_based_score(selected_new_user)
                
                if 'error' not in cluster_result:
                    st.success(f"✅ Clustering analysis for {selected_new_user[:10]}...")
                    
                    if 'cluster_assignments' in cluster_result:
                        assignments = cluster_result['cluster_assignments']
                        
                        # Display cluster assignments
                        for method, assignment in assignments.items():
                            st.subheader(f"{method.upper()} Clustering")
                            
                            if method == 'dbscan' and assignment.get('cluster_id') == -1:
                                st.warning("🔴 **Classified as Noise/Outlier**")
                                st.write(f"Suggested Initial Score: {assignment.get('suggested_initial_score', 'N/A')}")
                            else:
                                st.info(f"🔵 **Cluster ID**: {assignment.get('cluster_id', 'N/A')}")
                                
                                if 'cluster_size' in assignment:
                                    st.write(f"**Cluster Size**: {assignment['cluster_size']:,} users")
                                if 'avg_nfcs' in assignment:
                                    st.write(f"**Average NFCS**: {assignment['avg_nfcs']:.1f}")
                                if 'suggested_initial_score' in assignment:
                                    st.write(f"**Suggested Initial Score**: {assignment['suggested_initial_score']:.1f}")
                else:
                    st.error(f"Error: {cluster_result['error']}")
            except Exception as e:
                st.error(f"Error analyzing clustering: {e}")
    else:
        st.info("No new users (without loan history) found in the dataset.")

if __name__ == "__main__":
    main()
