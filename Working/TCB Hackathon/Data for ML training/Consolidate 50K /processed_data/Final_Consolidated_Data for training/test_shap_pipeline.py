#!/usr/bin/env python3
"""
Test script for SHAP-enhanced DeFi Credit Scoring Pipeline
"""

import sys
import os
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline

def main():
    print("🔍 Testing SHAP-Enhanced DeFi Credit Scoring Pipeline")
    print("=" * 60)
    
    try:
        # Initialize pipeline
        print("Initializing pipeline...")
        pipeline = DeFiCreditScoringPipeline(".")
        
        # Load and preprocess data
        print("Loading and preprocessing data...")
        pipeline.load_data()
        pipeline.preprocess_data()
        
        # Feature engineering
        print("Engineering features...")
        pipeline.engineer_features()
        pipeline.create_target_variable()
        
        # Train models
        print("Training models...")
        pipeline.train_models()
        
        # Setup SHAP explainers
        print("Setting up SHAP explainers...")
        pipeline.setup_shap_explainers()
        
        # Test SHAP explanation for sample wallets
        print("\n" + "=" * 60)
        print("Testing SHAP explanations...")
        
        sample_wallets = pipeline.features_df['walletAddress'].head(3).tolist()
        
        for i, wallet in enumerate(sample_wallets, 1):
            print(f"\n{i}. SHAP Analysis for wallet: {wallet[:10]}...")
            
            # Get SHAP explanation
            shap_explanation = pipeline.get_shap_explanation(wallet, 'random_forest')
            
            if 'error' not in shap_explanation:
                pred = shap_explanation['prediction']
                shap_info = shap_explanation['shap_explanation']
                
                print(f"   Risk Level: {pred['risk_level']}")
                print(f"   Risk Confidence: {max(pred['probabilities']):.1%}")
                print(f"   Expected Value: {shap_info['expected_value']:.3f}")
                print(f"   Prediction Value: {shap_info['prediction_value']:.3f}")
                print(f"   Total SHAP Impact: {shap_info['total_shap_contribution']:.3f}")
                
                # Top positive contributors
                if shap_explanation['top_positive_features']:
                    print(f"   Top Positive Features:")
                    for feat in shap_explanation['top_positive_features'][:3]:
                        print(f"     - {feat['feature']}: {feat['shap_value']:.4f}")
                
                # Top negative contributors
                if shap_explanation['top_negative_features']:
                    print(f"   Top Negative Features:")
                    for feat in shap_explanation['top_negative_features'][:3]:
                        print(f"     - {feat['feature']}: {feat['shap_value']:.4f}")
            else:
                print(f"   Error: {shap_explanation['error']}")
        
        # Generate SHAP plots
        print(f"\n" + "=" * 60)
        print("Generating SHAP visualization plots...")
        pipeline.plot_shap_analysis()
        
        print("\n✅ SHAP analysis completed successfully!")
        print("\nGenerated SHAP visualization files:")
        print("- shap_summary_plots.png")
        print("- shap_feature_importance.png") 
        print("- shap_waterfall_plots.png")
        print("- shap_dependence_plots.png")
        
        print(f"\n" + "=" * 60)
        print("🎉 All SHAP tests completed!")
        
    except Exception as e:
        print(f"❌ Error running SHAP pipeline: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
